"use client";

import { ChevronRight } from "lucide-react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@core/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@core/components/ui/sidebar";

import { UserSquare, UserCog, Cog } from "lucide-react";
import Link from "next/link";

export function NavMain({ items, selected_item }) {
  // const behavioural_skills = items.behavioural_skills;

  return (
    <SidebarGroup className={"pb-0"}>
      <SidebarGroupLabel className="min-w-8 bg-primary text-primary-foreground">
        Skills Library
      </SidebarGroupLabel>
      <SidebarMenu>
        <Collapsible
          key={1}
          asChild
          defaultOpen={
            selected_item && selected_item?.id === selected_item.id
              ? true
              : false
          }
          className="group/collapsible"
        >
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton tooltip="Behavioural skills" className="pt-4">
                <UserSquare />
                <span>{"Behavioural skills"}</span>
                <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                {items.map((item) => (
                  <SidebarMenuSubItem key={item.skill_name}>
                    <SidebarMenuSubButton asChild>
                      <Link href={"/behavioural/" + item.id}>
                        {selected_item && selected_item === item.skill_name ? (
                          <span className="text-primary font-bold">
                            {item.skill_name}
                          </span>
                        ) : (
                          <span>{item.skill_name}</span>
                        )}
                      </Link>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                ))}
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
      </SidebarMenu>
    </SidebarGroup>
  );
}
