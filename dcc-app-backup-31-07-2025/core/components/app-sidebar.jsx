import { useState } from "react";
import { supabase } from "@utils/supabaseClient";
import {
  LayoutDashboard,
  UserSquare,
  Cog,
  BookUser,
  Store,
} from "lucide-react";

import { useSidebar } from "@core/components/ui/sidebar";
import { TeamSwitcher } from "@core/components/ui/team-switcher";
import { NavMain } from "@core/components/ui/nav-main";
import { NavTech } from "@core/components/ui/nav-tech";
import { NavProjects } from "@core/components/ui/nav-projects";
import { NavRole } from "@core/components/ui/nav-role";
import { NavDashboard } from "@core/components/ui/nav-dashboard";
import { NavResources } from "@core/components/ui/nav-resources";
import { NavUser } from "@core/components/ui/nav-user";
import { Button } from "@core/components/ui/button";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
  SidebarInset,
  SidebarHeader,
  SidebarFooter,
  SidebarRail,
} from "@core/components/ui/sidebar";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@core/components/ui/breadcrumb";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@core/components/ui/select";

// Menu items.
const items = [
  {
    title: "Skills Library",
    url: "#",
    icon: LayoutDashboard,
    isActive: true,
    items: [
      {
        title: "History",
        url: "#",
      },
      {
        title: "Starred",
        url: "#",
      },
      {
        title: "Settings",
        url: "#",
      },
    ],
  },
];

const data = {
  dashboard: [
    {
      name: "Dashboard",
      url: "/dashboard",
      icon: LayoutDashboard,
    },
  ],

  projects: [
    // {
    //   name: "Function",
    //   url: "#",
    //   icon: Workflow,
    // },
    // {
    //   name: "Owner",
    //   url: "#",
    //   icon: User,
    // },
  ],
  role: [
    {
      name: "Roles",
      url: "/roles",
      icon: BookUser,
    },
  ],
  resources: [
    {
      name: "Talent Marketplace",
      url: "/talent-marketplace",
      icon: Store,
    },
    // {
    //   name: "Interview bank",
    //   url: "#",
    //   icon: SquareLibrary,
    // },
    // {
    //   name: "Learning resources",
    //   url: "#",
    //   icon: GraduationCap,
    // },
  ],
};

const algorithm = [
  "Cyber Security Operations",
  "Security Architecture and Assurance",
];
const language = [
  "Security Compliance, Risk and Resilience",
  "Security, Demand, Capability and Awareness",
];

export function AppSidebar({
  userData,
  behavioural_skills,
  technical_skills,
  selected_item,
  selected_item_tech,
}) {
  const {
    state,
    open,
    setOpen,
    openMobile,
    setOpenMobile,
    isMobile,
    toggleSidebar,
  } = useSidebar();

  const [selected, setSelected] = useState("");
  const [teamSelected, setTeamSelected] = useState(false);
  const [showSkills, setShowSkills] = useState(false);
  const [typeSelected, setTypeSelected] = useState("");

  const changeSelectOptionHandler = (value) => {
    setSelected(value);
    setShowSkills(false);
  };

  const teamSelectOptionHandler = (value) => {
    setTeamSelected(value);
    setShowSkills(true);
  };

  const typeSelector = (value) => {
    props.handleShowSkills(value);
  };

  /* --- DEBUG --- */
  // console.log("userData", userData);
  // console.log("behavioural_skills", behavioural_skills);
  // console.log("selected_item", selected_item);
  /* --- DEBUG --- */

  /** Type variable to store different array for different dropdown */
  let type = null;

  /** This will be used to create set of options that user will see */
  let options = null;

  /** Setting Type variable according to dropdown */
  if (selected === "Security") {
    type = algorithm;
  } else if (selected === "Another Security") {
    type = language;
  }

  /** If "Type" is null or undefined then options will be null,
   * otherwise it will create a options iterable based on our array
   */
  if (type) {
    options = type.map((el) => (
      <SelectItem key={el} value={el}>
        {el}
      </SelectItem>
    ));
  }

  return (
    <Sidebar collapsible="offcanvas">
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavDashboard projects={data.dashboard} />
        <NavMain items={behavioural_skills} selected_item={selected_item} />
        <NavTech
          items={technical_skills}
          selected_item_tech={selected_item_tech}
        />
        {/* <NavProjects projects={data.projects} /> */}
        <SidebarGroup />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userData} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
