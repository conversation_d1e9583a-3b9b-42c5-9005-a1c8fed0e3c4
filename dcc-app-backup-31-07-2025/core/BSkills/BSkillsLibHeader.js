import { Card, CardContent } from "@core/components/ui/card";

const BSkillsLibHeader = () => (
  <>
    <Card className="min-w-8 bg-[#ca005d] text-primary-foreground rounded-tl-xl rounded-tr-xl">
      <CardContent className={"font-extrabold text-1xl text-center"}>
        Think
      </CardContent>
    </Card>

    <Card className="min-w-8 bg-primary text-primary-foreground rounded-tl-xl rounded-tr-xl">
      <CardContent className={"font-extrabold text-1xl text-center"}>
        Operational Contributor
      </CardContent>
    </Card>

    <Card className="min-w-8  bg-primary text-primary-foreground rounded-tl-xl rounded-tr-xl">
      <CardContent className={"font-extrabold text-1xl text-center"}>
        Advanced Contributor
      </CardContent>
    </Card>

    <Card className="min-w-8 m bg-primary text-primary-foreground rounded-tl-xl rounded-tr-xl">
      <CardContent className={"font-extrabold text-1xl text-center"}>
        Team Leader
      </CardContent>
    </Card>

    <Card className="min-w-8  bg-primary text-primary-foreground rounded-tl-xl rounded-tr-xl">
      <CardContent className={"font-extrabold text-1xl text-center"}>
        Leader of Leaders
      </CardContent>
    </Card>

    <Card className="min-w-8 bg-primary text-primary-foreground rounded-tl-xl rounded-tr-xl">
      <CardContent className={"font-extrabold text-1xl text-center"}>
        Organisational Leader
      </CardContent>
    </Card>
  </>
);

export default BSkillsLibHeader;
