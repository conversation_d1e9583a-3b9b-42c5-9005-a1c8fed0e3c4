import { Card, CardContent } from "@core/components/ui/card";

const BSkillsLibHeader = ({ behavioural_sub_skills }) => {
  // put them in index order here
  behavioural_sub_skills.sort((a, b) => a.index - b.index);

  return (
    <>
      <Card className="min-w-8 min-h-58 bg-[#ca005d] text-primary-foreground">
        <CardContent className={"font-extrabold text-l text-center pt-16"}>
          <div className="inline-block align-middle">
            {behavioural_sub_skills[0].sub_skill_name}
          </div>
        </CardContent>
      </Card>

      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[0].level_1_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[0].level_2_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[0].level_3_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[0].level_4_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[0].level_5_description}
        </CardContent>
      </Card>

      <Card className="min-w-8 min-h-58 bg-[#ca005d] text-primary-foreground">
        <CardContent className={"font-extrabold text-l text-center pt-16"}>
          {behavioural_sub_skills[1].sub_skill_name}
        </CardContent>
      </Card>

      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[1].level_1_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[1].level_2_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[1].level_3_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[1].level_4_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[1].level_5_description}
        </CardContent>
      </Card>

      <Card className="min-w-8 min-h-58 bg-[#ca005d] text-primary-foreground">
        <CardContent className={"font-extrabold text-l text-center pt-16"}>
          <div className="inline-block align-middle">
            {" "}
            {behavioural_sub_skills[2].sub_skill_name}
          </div>
        </CardContent>
      </Card>

      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[2].level_1_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[2].level_2_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[2].level_3_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[2].level_4_description}
        </CardContent>
      </Card>
      <Card className="min-w-8 bg-muted/50 hover:bg-primary hover:text-white">
        <CardContent className="text-sm tracking-tight">
          {behavioural_sub_skills[2].level_5_description}
        </CardContent>
      </Card>
    </>
  );
};

export default BSkillsLibHeader;
