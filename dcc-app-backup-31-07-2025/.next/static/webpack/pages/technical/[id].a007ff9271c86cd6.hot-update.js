"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/technical/[id]",{

/***/ "(pages-dir-browser)/./core/components/app-sidebar-tech.jsx":
/*!**********************************************!*\
  !*** ./core/components/app-sidebar-tech.jsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebarTech: () => (/* binding */ AppSidebarTech)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookUser,Cog,LayoutDashboard,Store,UserSquare!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=BookUser,Cog,LayoutDashboard,Store,UserSquare!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_team_switcher__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @core/components/ui/team-switcher */ \"(pages-dir-browser)/./core/components/ui/team-switcher.jsx\");\n/* harmony import */ var _core_components_ui_nav_main__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @core/components/ui/nav-main */ \"(pages-dir-browser)/./core/components/ui/nav-main.jsx\");\n/* harmony import */ var _core_components_ui_nav_tech__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/nav-tech */ \"(pages-dir-browser)/./core/components/ui/nav-tech.jsx\");\n/* harmony import */ var _core_components_ui_nav_projects__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/nav-projects */ \"(pages-dir-browser)/./core/components/ui/nav-projects.jsx\");\n/* harmony import */ var _core_components_ui_nav_role__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/ui/nav-role */ \"(pages-dir-browser)/./core/components/ui/nav-role.jsx\");\n/* harmony import */ var _core_components_ui_nav_dashboard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/ui/nav-dashboard */ \"(pages-dir-browser)/./core/components/ui/nav-dashboard.jsx\");\n/* harmony import */ var _core_components_ui_nav_resources__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @core/components/ui/nav-resources */ \"(pages-dir-browser)/./core/components/ui/nav-resources.jsx\");\n/* harmony import */ var _core_components_ui_nav_user__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @core/components/ui/nav-user */ \"(pages-dir-browser)/./core/components/ui/nav-user.jsx\");\n/* harmony import */ var _core_components_ui_button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @core/components/ui/button */ \"(pages-dir-browser)/./core/components/ui/button.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_ui_select__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @core/components/ui/select */ \"(pages-dir-browser)/./core/components/ui/select.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Menu items.\nconst items = [\n    {\n        title: \"Skills Library\",\n        url: \"#\",\n        icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_lucide_react__WEBPACK_IMPORTED_MODULE_15__.LayoutDashboard,\n        isActive: true,\n        items: [\n            {\n                title: \"History\",\n                url: \"#\"\n            },\n            {\n                title: \"Starred\",\n                url: \"#\"\n            },\n            {\n                title: \"Settings\",\n                url: \"#\"\n            }\n        ]\n    }\n];\nconst data = {\n    dashboard: [\n        {\n            name: \"Dashboard\",\n            url: \"/dashboard\",\n            icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_lucide_react__WEBPACK_IMPORTED_MODULE_15__.LayoutDashboard\n        }\n    ],\n    projects: [],\n    role: [\n        {\n            name: \"Roles\",\n            url: \"/roles\",\n            icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_lucide_react__WEBPACK_IMPORTED_MODULE_15__.BookUser\n        }\n    ],\n    resources: [\n        {\n            name: \"Talent Marketplace\",\n            url: \"/talent-marketplace\",\n            icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_lucide_react__WEBPACK_IMPORTED_MODULE_15__.Store\n        }\n    ]\n};\nconst algorithm = [\n    \"Cyber Security Operations\",\n    \"Security Architecture and Assurance\"\n];\nconst language = [\n    \"Security Compliance, Risk and Resilience\",\n    \"Security, Demand, Capability and Awareness\"\n];\nfunction AppSidebarTech(param) {\n    let { userData, behavioural_skills, technical_skills, selected_item, selected_item_tech } = param;\n    _s();\n    const { state, open, setOpen, openMobile, setOpenMobile, isMobile, toggleSidebar } = (0,_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teamSelected, setTeamSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSkills, setShowSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [typeSelected, setTypeSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const changeSelectOptionHandler = (value)=>{\n        setSelected(value);\n        setShowSkills(false);\n    };\n    const teamSelectOptionHandler = (value)=>{\n        setTeamSelected(value);\n        setShowSkills(true);\n    };\n    const typeSelector = (value)=>{\n        props.handleShowSkills(value);\n    };\n    /* --- DEBUG --- */ // console.log(\"userData\", userData);\n    // console.log(\"behavioural_skills\", behavioural_skills);\n    // console.log(\"selected_item\", selected_item);\n    /* --- DEBUG --- */ /** Type variable to store different array for different dropdown */ let type = null;\n    /** This will be used to create set of options that user will see */ let options = null;\n    /** Setting Type variable according to dropdown */ if (selected === \"Security\") {\n        type = algorithm;\n    } else if (selected === \"Another Security\") {\n        type = language;\n    }\n    /** If \"Type\" is null or undefined then options will be null,\n   * otherwise it will create a options iterable based on our array\n   */ if (type) {\n        options = type.map((el)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                value: el,\n                children: el\n            }, el, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar-tech.jsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this));\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n        collapsible: \"offcanvas\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_team_switcher__WEBPACK_IMPORTED_MODULE_4__.TeamSwitcher, {\n                    teams: data.teams\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar-tech.jsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar-tech.jsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroup, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar-tech.jsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_nav_dashboard__WEBPACK_IMPORTED_MODULE_9__.NavDashboard, {\n                        projects: data.dashboard\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar-tech.jsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_nav_main__WEBPACK_IMPORTED_MODULE_5__.NavMain, {\n                        items: behavioural_skills,\n                        selected_item: selected_item\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar-tech.jsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_nav_tech__WEBPACK_IMPORTED_MODULE_6__.NavTech, {\n                        items: technical_skills,\n                        selected_item: selected_item\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar-tech.jsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroup, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar-tech.jsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar-tech.jsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_nav_user__WEBPACK_IMPORTED_MODULE_11__.NavUser, {\n                    user: userData\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar-tech.jsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar-tech.jsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarRail, {}, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar-tech.jsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar-tech.jsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(AppSidebarTech, \"aMTQ7+GZT+4mmFaQavXTNoT1a/U=\", false, function() {\n    return [\n        _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar\n    ];\n});\n_c = AppSidebarTech;\nvar _c;\n$RefreshReg$(_c, \"AppSidebarTech\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./core/components/app-sidebar-tech.jsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./pages/technical/[id].js":
/*!*********************************!*\
  !*** ./pages/technical/[id].js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Technical)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_ui_popover__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/popover */ \"(pages-dir-browser)/./core/components/ui/popover.jsx\");\n/* harmony import */ var _core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/ui/card */ \"(pages-dir-browser)/./core/components/ui/card.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n/* harmony import */ var core_TSkillsLib__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core/TSkillsLib */ \"(pages-dir-browser)/./core/TSkillsLib.js\");\n/* harmony import */ var core_TSkills_TSkillsLibHeader__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core/TSkills/TSkillsLibHeader */ \"(pages-dir-browser)/./core/TSkills/TSkillsLibHeader.js\");\n/* harmony import */ var core_TSkills_TSkillRow__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core/TSkills/TSkillRow */ \"(pages-dir-browser)/./core/TSkills/TSkillRow.js\");\n/* harmony import */ var _core_components_app_sidebar_tech__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @core/components/app-sidebar-tech */ \"(pages-dir-browser)/./core/components/app-sidebar-tech.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Technical(param) {\n    let { behavioural_skills, behavioural_skills_menu, technical_skills, technical_skills_menu } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Technical.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession().then({\n                \"Technical.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Technical.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange({\n                \"Technical.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Technical.useEffect\"]);\n            return ({\n                \"Technical.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Technical.useEffect\"];\n        }\n    }[\"Technical.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Technical.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Technical.useEffect\"], [\n        session\n    ]);\n    /* --- DEBUG --- */ // console.log(\"technical_skills\");\n    // console.log(technical_skills);\n    // console.log(\"technical_skills_menu\");\n    // console.log(technical_skills_menu);\n    // console.log(\"behavioural_skills_menu\");\n    // console.log(behavioural_skills_menu);\n    // console.log(\"technical_skills.skill_name\");\n    // console.log(technical_skills.skill_name);\n    /* --- DEBUG --- */ async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                        lineNumber: 139,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_9__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills_menu,\n                technical_skills: technical_skills_menu,\n                selected_item_tech: technical_skills.skill_name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarTrigger, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                                    href: \"#\",\n                                                    children: \"Skills Library\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                                    children: \"Technical\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbPage, {\n                                                    children: technical_skills.skill_name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 px-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_7__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_7__.PopoverTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"border border-gray-400 p-1.5 rounded-md text-sm\",\n                                                    children: \"Overview\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_7__.PopoverContent, {\n                                                children: technical_skills.overview\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_TSkillsLib__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_TSkills_TSkillsLibHeader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(core_TSkills_TSkillRow__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    skill_name: technical_skills.skill_name,\n                                    technical_sub_skills: technical_skills.technical_sub_skills\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/technical/[id].js\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(Technical, \"KNaG7HRFKj3fWN5peDVgbtbg7mo=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Technical;\nvar _c;\n$RefreshReg$(_c, \"Technical\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/technical/[id].js\n"));

/***/ })

});