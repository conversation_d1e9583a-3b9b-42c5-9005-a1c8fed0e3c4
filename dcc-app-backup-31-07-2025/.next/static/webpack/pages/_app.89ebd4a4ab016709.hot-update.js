"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\\n@layer properties;\\n@layer theme, base, components, utilities;\\n@layer theme {\\n  :root, :host {\\n    --color-green-500: oklch(72.3% 0.219 149.579);\\n    --color-green-700: oklch(52.7% 0.154 150.069);\\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\\n    --color-slate-200: oklch(92.9% 0.013 255.508);\\n    --color-slate-300: oklch(86.9% 0.022 252.894);\\n    --color-slate-400: oklch(70.4% 0.04 256.788);\\n    --color-slate-700: oklch(37.2% 0.044 257.287);\\n    --color-slate-800: oklch(27.9% 0.041 260.031);\\n    --color-gray-300: oklch(87.2% 0.01 258.338);\\n    --color-gray-400: oklch(70.7% 0.022 261.325);\\n    --color-gray-700: oklch(37.3% 0.034 259.733);\\n    --color-gray-900: oklch(21% 0.034 264.665);\\n    --color-black: #000;\\n    --color-white: #fff;\\n    --spacing: 0.25rem;\\n    --container-sm: 24rem;\\n    --container-md: 28rem;\\n    --text-xs: 0.75rem;\\n    --text-xs--line-height: calc(1 / 0.75);\\n    --text-sm: 0.875rem;\\n    --text-sm--line-height: calc(1.25 / 0.875);\\n    --text-base: 1rem;\\n    --text-base--line-height: calc(1.5 / 1);\\n    --text-lg: 1.125rem;\\n    --text-lg--line-height: calc(1.75 / 1.125);\\n    --text-2xl: 1.5rem;\\n    --text-2xl--line-height: calc(2 / 1.5);\\n    --font-weight-normal: 400;\\n    --font-weight-medium: 500;\\n    --font-weight-semibold: 600;\\n    --font-weight-bold: 700;\\n    --font-weight-extrabold: 800;\\n    --tracking-tight: -0.025em;\\n    --tracking-widest: 0.1em;\\n    --leading-tight: 1.25;\\n    --radius-xs: 0.125rem;\\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n    --default-transition-duration: 150ms;\\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    --default-font-family: var(--font-geist-sans);\\n    --default-mono-font-family: var(--font-geist-mono);\\n  }\\n}\\n@layer base {\\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\\n    box-sizing: border-box;\\n    margin: 0;\\n    padding: 0;\\n    border: 0 solid;\\n  }\\n  html, :host {\\n    line-height: 1.5;\\n    -webkit-text-size-adjust: 100%;\\n    tab-size: 4;\\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\");\\n    font-feature-settings: var(--default-font-feature-settings, normal);\\n    font-variation-settings: var(--default-font-variation-settings, normal);\\n    -webkit-tap-highlight-color: transparent;\\n  }\\n  hr {\\n    height: 0;\\n    color: inherit;\\n    border-top-width: 1px;\\n  }\\n  abbr:where([title]) {\\n    -webkit-text-decoration: underline dotted;\\n    text-decoration: underline dotted;\\n  }\\n  h1, h2, h3, h4, h5, h6 {\\n    font-size: inherit;\\n    font-weight: inherit;\\n  }\\n  a {\\n    color: inherit;\\n    -webkit-text-decoration: inherit;\\n    text-decoration: inherit;\\n  }\\n  b, strong {\\n    font-weight: bolder;\\n  }\\n  code, kbd, samp, pre {\\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace);\\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\\n    font-size: 1em;\\n  }\\n  small {\\n    font-size: 80%;\\n  }\\n  sub, sup {\\n    font-size: 75%;\\n    line-height: 0;\\n    position: relative;\\n    vertical-align: baseline;\\n  }\\n  sub {\\n    bottom: -0.25em;\\n  }\\n  sup {\\n    top: -0.5em;\\n  }\\n  table {\\n    text-indent: 0;\\n    border-color: inherit;\\n    border-collapse: collapse;\\n  }\\n  :-moz-focusring {\\n    outline: auto;\\n  }\\n  progress {\\n    vertical-align: baseline;\\n  }\\n  summary {\\n    display: list-item;\\n  }\\n  ol, ul, menu {\\n    list-style: none;\\n  }\\n  img, svg, video, canvas, audio, iframe, embed, object {\\n    display: block;\\n    vertical-align: middle;\\n  }\\n  img, video {\\n    max-width: 100%;\\n    height: auto;\\n  }\\n  button, input, select, optgroup, textarea, ::file-selector-button {\\n    font: inherit;\\n    font-feature-settings: inherit;\\n    font-variation-settings: inherit;\\n    letter-spacing: inherit;\\n    color: inherit;\\n    border-radius: 0;\\n    background-color: transparent;\\n    opacity: 1;\\n  }\\n  :where(select:is([multiple], [size])) optgroup {\\n    font-weight: bolder;\\n  }\\n  :where(select:is([multiple], [size])) optgroup option {\\n    padding-inline-start: 20px;\\n  }\\n  ::file-selector-button {\\n    margin-inline-end: 4px;\\n  }\\n  ::placeholder {\\n    opacity: 1;\\n  }\\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\\n    ::placeholder {\\n      color: currentcolor;\\n      @supports (color: color-mix(in lab, red, red)) {\\n        color: color-mix(in oklab, currentcolor 50%, transparent);\\n      }\\n    }\\n  }\\n  textarea {\\n    resize: vertical;\\n  }\\n  ::-webkit-search-decoration {\\n    -webkit-appearance: none;\\n  }\\n  ::-webkit-date-and-time-value {\\n    min-height: 1lh;\\n    text-align: inherit;\\n  }\\n  ::-webkit-datetime-edit {\\n    display: inline-flex;\\n  }\\n  ::-webkit-datetime-edit-fields-wrapper {\\n    padding: 0;\\n  }\\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\\n    padding-block: 0;\\n  }\\n  :-moz-ui-invalid {\\n    box-shadow: none;\\n  }\\n  button, input:where([type=\\\"button\\\"], [type=\\\"reset\\\"], [type=\\\"submit\\\"]), ::file-selector-button {\\n    appearance: button;\\n  }\\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\\n    height: auto;\\n  }\\n  [hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n    display: none !important;\\n  }\\n}\\n@layer utilities {\\n  .\\\\@container\\\\/card-header {\\n    container-type: inline-size;\\n    container-name: card-header;\\n  }\\n  .pointer-events-none {\\n    pointer-events: none;\\n  }\\n  .invisible {\\n    visibility: hidden;\\n  }\\n  .sr-only {\\n    position: absolute;\\n    width: 1px;\\n    height: 1px;\\n    padding: 0;\\n    margin: -1px;\\n    overflow: hidden;\\n    clip: rect(0, 0, 0, 0);\\n    white-space: nowrap;\\n    border-width: 0;\\n  }\\n  .absolute {\\n    position: absolute;\\n  }\\n  .fixed {\\n    position: fixed;\\n  }\\n  .relative {\\n    position: relative;\\n  }\\n  .inset-0 {\\n    inset: calc(var(--spacing) * 0);\\n  }\\n  .inset-x-0 {\\n    inset-inline: calc(var(--spacing) * 0);\\n  }\\n  .inset-y-0 {\\n    inset-block: calc(var(--spacing) * 0);\\n  }\\n  .-top-12 {\\n    top: calc(var(--spacing) * -12);\\n  }\\n  .top-0 {\\n    top: calc(var(--spacing) * 0);\\n  }\\n  .top-0\\\\.5 {\\n    top: calc(var(--spacing) * 0.5);\\n  }\\n  .top-1\\\\.5 {\\n    top: calc(var(--spacing) * 1.5);\\n  }\\n  .top-1\\\\/2 {\\n    top: calc(1/2 * 100%);\\n  }\\n  .top-2 {\\n    top: calc(var(--spacing) * 2);\\n  }\\n  .top-3\\\\.5 {\\n    top: calc(var(--spacing) * 3.5);\\n  }\\n  .top-4 {\\n    top: calc(var(--spacing) * 4);\\n  }\\n  .-right-12 {\\n    right: calc(var(--spacing) * -12);\\n  }\\n  .right-0 {\\n    right: calc(var(--spacing) * 0);\\n  }\\n  .right-0\\\\.5 {\\n    right: calc(var(--spacing) * 0.5);\\n  }\\n  .right-1 {\\n    right: calc(var(--spacing) * 1);\\n  }\\n  .right-2 {\\n    right: calc(var(--spacing) * 2);\\n  }\\n  .right-3 {\\n    right: calc(var(--spacing) * 3);\\n  }\\n  .right-4 {\\n    right: calc(var(--spacing) * 4);\\n  }\\n  .-bottom-12 {\\n    bottom: calc(var(--spacing) * -12);\\n  }\\n  .bottom-0 {\\n    bottom: calc(var(--spacing) * 0);\\n  }\\n  .-left-12 {\\n    left: calc(var(--spacing) * -12);\\n  }\\n  .left-0 {\\n    left: calc(var(--spacing) * 0);\\n  }\\n  .left-1\\\\/2 {\\n    left: calc(1/2 * 100%);\\n  }\\n  .left-2 {\\n    left: calc(var(--spacing) * 2);\\n  }\\n  .z-10 {\\n    z-index: 10;\\n  }\\n  .z-20 {\\n    z-index: 20;\\n  }\\n  .z-50 {\\n    z-index: 50;\\n  }\\n  .col-start-2 {\\n    grid-column-start: 2;\\n  }\\n  .row-span-2 {\\n    grid-row: span 2 / span 2;\\n  }\\n  .row-start-1 {\\n    grid-row-start: 1;\\n  }\\n  .m-6 {\\n    margin: calc(var(--spacing) * 6);\\n  }\\n  .m-auto {\\n    margin: auto;\\n  }\\n  .-mx-1 {\\n    margin-inline: calc(var(--spacing) * -1);\\n  }\\n  .mx-2 {\\n    margin-inline: calc(var(--spacing) * 2);\\n  }\\n  .mx-3\\\\.5 {\\n    margin-inline: calc(var(--spacing) * 3.5);\\n  }\\n  .mx-auto {\\n    margin-inline: auto;\\n  }\\n  .my-1 {\\n    margin-block: calc(var(--spacing) * 1);\\n  }\\n  .-mt-4 {\\n    margin-top: calc(var(--spacing) * -4);\\n  }\\n  .mt-0 {\\n    margin-top: calc(var(--spacing) * 0);\\n  }\\n  .mt-1 {\\n    margin-top: calc(var(--spacing) * 1);\\n  }\\n  .mt-4 {\\n    margin-top: calc(var(--spacing) * 4);\\n  }\\n  .mt-6 {\\n    margin-top: calc(var(--spacing) * 6);\\n  }\\n  .mt-8 {\\n    margin-top: calc(var(--spacing) * 8);\\n  }\\n  .mt-10 {\\n    margin-top: calc(var(--spacing) * 10);\\n  }\\n  .mt-auto {\\n    margin-top: auto;\\n  }\\n  .mb-2 {\\n    margin-bottom: calc(var(--spacing) * 2);\\n  }\\n  .mb-6 {\\n    margin-bottom: calc(var(--spacing) * 6);\\n  }\\n  .mb-10 {\\n    margin-bottom: calc(var(--spacing) * 10);\\n  }\\n  .-ml-4 {\\n    margin-left: calc(var(--spacing) * -4);\\n  }\\n  .ml-1 {\\n    margin-left: calc(var(--spacing) * 1);\\n  }\\n  .ml-6 {\\n    margin-left: calc(var(--spacing) * 6);\\n  }\\n  .ml-auto {\\n    margin-left: auto;\\n  }\\n  .block {\\n    display: block;\\n  }\\n  .flex {\\n    display: flex;\\n  }\\n  .grid {\\n    display: grid;\\n  }\\n  .hidden {\\n    display: none;\\n  }\\n  .inline-block {\\n    display: inline-block;\\n  }\\n  .inline-flex {\\n    display: inline-flex;\\n  }\\n  .table {\\n    display: table;\\n  }\\n  .table-caption {\\n    display: table-caption;\\n  }\\n  .table-cell {\\n    display: table-cell;\\n  }\\n  .table-row {\\n    display: table-row;\\n  }\\n  .aspect-square {\\n    aspect-ratio: 1 / 1;\\n  }\\n  .size-2 {\\n    width: calc(var(--spacing) * 2);\\n    height: calc(var(--spacing) * 2);\\n  }\\n  .size-2\\\\.5 {\\n    width: calc(var(--spacing) * 2.5);\\n    height: calc(var(--spacing) * 2.5);\\n  }\\n  .size-3\\\\.5 {\\n    width: calc(var(--spacing) * 3.5);\\n    height: calc(var(--spacing) * 3.5);\\n  }\\n  .size-4 {\\n    width: calc(var(--spacing) * 4);\\n    height: calc(var(--spacing) * 4);\\n  }\\n  .size-6 {\\n    width: calc(var(--spacing) * 6);\\n    height: calc(var(--spacing) * 6);\\n  }\\n  .size-7 {\\n    width: calc(var(--spacing) * 7);\\n    height: calc(var(--spacing) * 7);\\n  }\\n  .size-8 {\\n    width: calc(var(--spacing) * 8);\\n    height: calc(var(--spacing) * 8);\\n  }\\n  .size-9 {\\n    width: calc(var(--spacing) * 9);\\n    height: calc(var(--spacing) * 9);\\n  }\\n  .size-full {\\n    width: 100%;\\n    height: 100%;\\n  }\\n  .h-4 {\\n    height: calc(var(--spacing) * 4);\\n  }\\n  .h-5 {\\n    height: calc(var(--spacing) * 5);\\n  }\\n  .h-7 {\\n    height: calc(var(--spacing) * 7);\\n  }\\n  .h-8 {\\n    height: calc(var(--spacing) * 8);\\n  }\\n  .h-9 {\\n    height: calc(var(--spacing) * 9);\\n  }\\n  .h-10 {\\n    height: calc(var(--spacing) * 10);\\n  }\\n  .h-12 {\\n    height: calc(var(--spacing) * 12);\\n  }\\n  .h-16 {\\n    height: calc(var(--spacing) * 16);\\n  }\\n  .h-\\\\[calc\\\\(100\\\\%-1px\\\\)\\\\] {\\n    height: calc(100% - 1px);\\n  }\\n  .h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\] {\\n    height: var(--radix-select-trigger-height);\\n  }\\n  .h-auto {\\n    height: auto;\\n  }\\n  .h-full {\\n    height: 100%;\\n  }\\n  .h-px {\\n    height: 1px;\\n  }\\n  .h-screen {\\n    height: 100vh;\\n  }\\n  .h-svh {\\n    height: 100svh;\\n  }\\n  .max-h-\\\\(--radix-dropdown-menu-content-available-height\\\\) {\\n    max-height: var(--radix-dropdown-menu-content-available-height);\\n  }\\n  .max-h-\\\\(--radix-select-content-available-height\\\\) {\\n    max-height: var(--radix-select-content-available-height);\\n  }\\n  .min-h-0 {\\n    min-height: calc(var(--spacing) * 0);\\n  }\\n  .min-h-58 {\\n    min-height: calc(var(--spacing) * 58);\\n  }\\n  .min-h-svh {\\n    min-height: 100svh;\\n  }\\n  .w-\\\\(--sidebar-width\\\\) {\\n    width: var(--sidebar-width);\\n  }\\n  .w-3\\\\/4 {\\n    width: calc(3/4 * 100%);\\n  }\\n  .w-4 {\\n    width: calc(var(--spacing) * 4);\\n  }\\n  .w-5 {\\n    width: calc(var(--spacing) * 5);\\n  }\\n  .w-8 {\\n    width: calc(var(--spacing) * 8);\\n  }\\n  .w-28 {\\n    width: calc(var(--spacing) * 28);\\n  }\\n  .w-42 {\\n    width: calc(var(--spacing) * 42);\\n  }\\n  .w-48 {\\n    width: calc(var(--spacing) * 48);\\n  }\\n  .w-52 {\\n    width: calc(var(--spacing) * 52);\\n  }\\n  .w-64 {\\n    width: calc(var(--spacing) * 64);\\n  }\\n  .w-72 {\\n    width: calc(var(--spacing) * 72);\\n  }\\n  .w-\\\\[--radix-dropdown-menu-trigger-width\\\\] {\\n    width: --radix-dropdown-menu-trigger-width;\\n  }\\n  .w-\\\\[220px\\\\] {\\n    width: 220px;\\n  }\\n  .w-auto {\\n    width: auto;\\n  }\\n  .w-fit {\\n    width: fit-content;\\n  }\\n  .w-full {\\n    width: 100%;\\n  }\\n  .max-w-\\\\(--skeleton-width\\\\) {\\n    max-width: var(--skeleton-width);\\n  }\\n  .max-w-sm {\\n    max-width: var(--container-sm);\\n  }\\n  .min-w-0 {\\n    min-width: calc(var(--spacing) * 0);\\n  }\\n  .min-w-5 {\\n    min-width: calc(var(--spacing) * 5);\\n  }\\n  .min-w-8 {\\n    min-width: calc(var(--spacing) * 8);\\n  }\\n  .min-w-56 {\\n    min-width: calc(var(--spacing) * 56);\\n  }\\n  .min-w-\\\\[8rem\\\\] {\\n    min-width: 8rem;\\n  }\\n  .min-w-\\\\[12rem\\\\] {\\n    min-width: 12rem;\\n  }\\n  .min-w-\\\\[20px\\\\] {\\n    min-width: 20px;\\n  }\\n  .min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\] {\\n    min-width: var(--radix-select-trigger-width);\\n  }\\n  .flex-1 {\\n    flex: 1;\\n  }\\n  .shrink-0 {\\n    flex-shrink: 0;\\n  }\\n  .grow-0 {\\n    flex-grow: 0;\\n  }\\n  .basis-full {\\n    flex-basis: 100%;\\n  }\\n  .caption-bottom {\\n    caption-side: bottom;\\n  }\\n  .origin-\\\\(--radix-dropdown-menu-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-dropdown-menu-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-hover-card-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-hover-card-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-menubar-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-menubar-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-popover-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-popover-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-select-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-select-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-tooltip-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-tooltip-content-transform-origin);\\n  }\\n  .-translate-x-1\\\\/2 {\\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .-translate-x-px {\\n    --tw-translate-x: -1px;\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .translate-x-px {\\n    --tw-translate-x: 1px;\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .-translate-y-1\\\\/2 {\\n    --tw-translate-y: calc(calc(1/2 * 100%) * -1);\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .translate-y-0\\\\.5 {\\n    --tw-translate-y: calc(var(--spacing) * 0.5);\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .translate-y-\\\\[calc\\\\(-50\\\\%_-_2px\\\\)\\\\] {\\n    --tw-translate-y: calc(-50% - 2px);\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .rotate-45 {\\n    rotate: 45deg;\\n  }\\n  .rotate-90 {\\n    rotate: 90deg;\\n  }\\n  .transform {\\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\\n  }\\n  .animate-in {\\n    animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\\n  }\\n  .animate-pulse {\\n    animation: var(--animate-pulse);\\n  }\\n  .cursor-default {\\n    cursor: default;\\n  }\\n  .scroll-my-1 {\\n    scroll-margin-block: calc(var(--spacing) * 1);\\n  }\\n  .appearance-none {\\n    appearance: none;\\n  }\\n  .auto-rows-min {\\n    grid-auto-rows: min-content;\\n  }\\n  .grid-cols-\\\\[10\\\\%_18\\\\%_18\\\\%_18\\\\%_18\\\\%_18\\\\%\\\\] {\\n    grid-template-columns: 10% 18% 18% 18% 18% 18%;\\n  }\\n  .grid-cols-\\\\[20\\\\%_20\\\\%_20\\\\%_20\\\\%_20\\\\%\\\\] {\\n    grid-template-columns: 20% 20% 20% 20% 20%;\\n  }\\n  .grid-rows-\\\\[auto_auto\\\\] {\\n    grid-template-rows: auto auto;\\n  }\\n  .flex-col {\\n    flex-direction: column;\\n  }\\n  .flex-row {\\n    flex-direction: row;\\n  }\\n  .flex-wrap {\\n    flex-wrap: wrap;\\n  }\\n  .place-items-center {\\n    place-items: center;\\n  }\\n  .items-center {\\n    align-items: center;\\n  }\\n  .items-start {\\n    align-items: flex-start;\\n  }\\n  .justify-between {\\n    justify-content: space-between;\\n  }\\n  .justify-center {\\n    justify-content: center;\\n  }\\n  .gap-0 {\\n    gap: calc(var(--spacing) * 0);\\n  }\\n  .gap-1 {\\n    gap: calc(var(--spacing) * 1);\\n  }\\n  .gap-1\\\\.5 {\\n    gap: calc(var(--spacing) * 1.5);\\n  }\\n  .gap-2 {\\n    gap: calc(var(--spacing) * 2);\\n  }\\n  .gap-3 {\\n    gap: calc(var(--spacing) * 3);\\n  }\\n  .gap-4 {\\n    gap: calc(var(--spacing) * 4);\\n  }\\n  .gap-6 {\\n    gap: calc(var(--spacing) * 6);\\n  }\\n  .space-x-2 {\\n    :where(& > :not(:last-child)) {\\n      --tw-space-x-reverse: 0;\\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\\n    }\\n  }\\n  .self-start {\\n    align-self: flex-start;\\n  }\\n  .justify-self-end {\\n    justify-self: flex-end;\\n  }\\n  .truncate {\\n    overflow: hidden;\\n    text-overflow: ellipsis;\\n    white-space: nowrap;\\n  }\\n  .overflow-auto {\\n    overflow: auto;\\n  }\\n  .overflow-hidden {\\n    overflow: hidden;\\n  }\\n  .overflow-x-auto {\\n    overflow-x: auto;\\n  }\\n  .overflow-x-hidden {\\n    overflow-x: hidden;\\n  }\\n  .overflow-y-auto {\\n    overflow-y: auto;\\n  }\\n  .rounded {\\n    border-radius: 0.25rem;\\n  }\\n  .rounded-\\\\[2px\\\\] {\\n    border-radius: 2px;\\n  }\\n  .rounded-full {\\n    border-radius: calc(infinity * 1px);\\n  }\\n  .rounded-lg {\\n    border-radius: var(--radius);\\n  }\\n  .rounded-md {\\n    border-radius: calc(var(--radius) - 2px);\\n  }\\n  .rounded-sm {\\n    border-radius: calc(var(--radius) - 4px);\\n  }\\n  .rounded-xl {\\n    border-radius: calc(var(--radius) + 4px);\\n  }\\n  .rounded-xs {\\n    border-radius: var(--radius-xs);\\n  }\\n  .rounded-l-full {\\n    border-top-left-radius: calc(infinity * 1px);\\n    border-bottom-left-radius: calc(infinity * 1px);\\n  }\\n  .rounded-tl-xl {\\n    border-top-left-radius: calc(var(--radius) + 4px);\\n  }\\n  .rounded-tr-xl {\\n    border-top-right-radius: calc(var(--radius) + 4px);\\n  }\\n  .border {\\n    border-style: var(--tw-border-style);\\n    border-width: 1px;\\n  }\\n  .border-t {\\n    border-top-style: var(--tw-border-style);\\n    border-top-width: 1px;\\n  }\\n  .border-r {\\n    border-right-style: var(--tw-border-style);\\n    border-right-width: 1px;\\n  }\\n  .border-b {\\n    border-bottom-style: var(--tw-border-style);\\n    border-bottom-width: 1px;\\n  }\\n  .border-l {\\n    border-left-style: var(--tw-border-style);\\n    border-left-width: 1px;\\n  }\\n  .border-gray-300 {\\n    border-color: var(--color-gray-300);\\n  }\\n  .border-gray-400 {\\n    border-color: var(--color-gray-400);\\n  }\\n  .border-input {\\n    border-color: var(--input);\\n  }\\n  .border-sidebar-border {\\n    border-color: var(--sidebar-border);\\n  }\\n  .border-slate-200 {\\n    border-color: var(--color-slate-200);\\n  }\\n  .border-transparent {\\n    border-color: transparent;\\n  }\\n  .bg-\\\\[\\\\#1f144a\\\\] {\\n    background-color: #1f144a;\\n  }\\n  .bg-\\\\[\\\\#5c2071\\\\] {\\n    background-color: #5c2071;\\n  }\\n  .bg-\\\\[\\\\#009cbb\\\\] {\\n    background-color: #009cbb;\\n  }\\n  .bg-\\\\[\\\\#9ca299\\\\] {\\n    background-color: #9ca299;\\n  }\\n  .bg-\\\\[\\\\#95c11f\\\\] {\\n    background-color: #95c11f;\\n  }\\n  .bg-\\\\[\\\\#505253\\\\] {\\n    background-color: #505253;\\n  }\\n  .bg-\\\\[\\\\#ca005d\\\\] {\\n    background-color: #ca005d;\\n  }\\n  .bg-accent {\\n    background-color: var(--accent);\\n  }\\n  .bg-background {\\n    background-color: var(--background);\\n  }\\n  .bg-black\\\\/50 {\\n    background-color: color-mix(in srgb, #000 50%, transparent);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\\n    }\\n  }\\n  .bg-border {\\n    background-color: var(--border);\\n  }\\n  .bg-card {\\n    background-color: var(--card);\\n  }\\n  .bg-dccgreen {\\n    background-color: var(--dccgreen);\\n  }\\n  .bg-dcclightblue {\\n    background-color: var(--dcclightblue);\\n  }\\n  .bg-dccorange {\\n    background-color: var(--dccorange);\\n  }\\n  .bg-destructive {\\n    background-color: var(--destructive);\\n  }\\n  .bg-muted {\\n    background-color: var(--muted);\\n  }\\n  .bg-muted\\\\/50 {\\n    background-color: var(--muted);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      background-color: color-mix(in oklab, var(--muted) 50%, transparent);\\n    }\\n  }\\n  .bg-muted\\\\/100 {\\n    background-color: var(--muted);\\n  }\\n  .bg-popover {\\n    background-color: var(--popover);\\n  }\\n  .bg-primary {\\n    background-color: var(--primary);\\n  }\\n  .bg-primary\\\\/20 {\\n    background-color: var(--primary);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      background-color: color-mix(in oklab, var(--primary) 20%, transparent);\\n    }\\n  }\\n  .bg-secondary {\\n    background-color: var(--secondary);\\n  }\\n  .bg-sidebar {\\n    background-color: var(--sidebar);\\n  }\\n  .bg-sidebar-border {\\n    background-color: var(--sidebar-border);\\n  }\\n  .bg-sidebar-primary {\\n    background-color: var(--sidebar-primary);\\n  }\\n  .bg-slate-800 {\\n    background-color: var(--color-slate-800);\\n  }\\n  .bg-transparent {\\n    background-color: transparent;\\n  }\\n  .bg-white {\\n    background-color: var(--color-white);\\n  }\\n  .fill-current {\\n    fill: currentcolor;\\n  }\\n  .fill-primary {\\n    fill: var(--primary);\\n  }\\n  .p-0 {\\n    padding: calc(var(--spacing) * 0);\\n  }\\n  .p-1 {\\n    padding: calc(var(--spacing) * 1);\\n  }\\n  .p-1\\\\.5 {\\n    padding: calc(var(--spacing) * 1.5);\\n  }\\n  .p-2 {\\n    padding: calc(var(--spacing) * 2);\\n  }\\n  .p-3 {\\n    padding: calc(var(--spacing) * 3);\\n  }\\n  .p-4 {\\n    padding: calc(var(--spacing) * 4);\\n  }\\n  .p-6 {\\n    padding: calc(var(--spacing) * 6);\\n  }\\n  .p-\\\\[3px\\\\] {\\n    padding: 3px;\\n  }\\n  .px-1 {\\n    padding-inline: calc(var(--spacing) * 1);\\n  }\\n  .px-2 {\\n    padding-inline: calc(var(--spacing) * 2);\\n  }\\n  .px-2\\\\.5 {\\n    padding-inline: calc(var(--spacing) * 2.5);\\n  }\\n  .px-3 {\\n    padding-inline: calc(var(--spacing) * 3);\\n  }\\n  .px-4 {\\n    padding-inline: calc(var(--spacing) * 4);\\n  }\\n  .px-6 {\\n    padding-inline: calc(var(--spacing) * 6);\\n  }\\n  .py-0\\\\.5 {\\n    padding-block: calc(var(--spacing) * 0.5);\\n  }\\n  .py-1 {\\n    padding-block: calc(var(--spacing) * 1);\\n  }\\n  .py-1\\\\.5 {\\n    padding-block: calc(var(--spacing) * 1.5);\\n  }\\n  .py-2 {\\n    padding-block: calc(var(--spacing) * 2);\\n  }\\n  .py-3 {\\n    padding-block: calc(var(--spacing) * 3);\\n  }\\n  .py-4 {\\n    padding-block: calc(var(--spacing) * 4);\\n  }\\n  .py-6 {\\n    padding-block: calc(var(--spacing) * 6);\\n  }\\n  .pt-0 {\\n    padding-top: calc(var(--spacing) * 0);\\n  }\\n  .pt-1 {\\n    padding-top: calc(var(--spacing) * 1);\\n  }\\n  .pt-4 {\\n    padding-top: calc(var(--spacing) * 4);\\n  }\\n  .pt-6 {\\n    padding-top: calc(var(--spacing) * 6);\\n  }\\n  .pt-16 {\\n    padding-top: calc(var(--spacing) * 16);\\n  }\\n  .pr-2 {\\n    padding-right: calc(var(--spacing) * 2);\\n  }\\n  .pr-8 {\\n    padding-right: calc(var(--spacing) * 8);\\n  }\\n  .pb-0 {\\n    padding-bottom: calc(var(--spacing) * 0);\\n  }\\n  .pb-2 {\\n    padding-bottom: calc(var(--spacing) * 2);\\n  }\\n  .pb-4 {\\n    padding-bottom: calc(var(--spacing) * 4);\\n  }\\n  .pl-2 {\\n    padding-left: calc(var(--spacing) * 2);\\n  }\\n  .pl-2\\\\.5 {\\n    padding-left: calc(var(--spacing) * 2.5);\\n  }\\n  .pl-3 {\\n    padding-left: calc(var(--spacing) * 3);\\n  }\\n  .pl-4 {\\n    padding-left: calc(var(--spacing) * 4);\\n  }\\n  .pl-8 {\\n    padding-left: calc(var(--spacing) * 8);\\n  }\\n  .text-center {\\n    text-align: center;\\n  }\\n  .text-left {\\n    text-align: left;\\n  }\\n  .align-middle {\\n    vertical-align: middle;\\n  }\\n  .text-2xl {\\n    font-size: var(--text-2xl);\\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\\n  }\\n  .text-base {\\n    font-size: var(--text-base);\\n    line-height: var(--tw-leading, var(--text-base--line-height));\\n  }\\n  .text-lg {\\n    font-size: var(--text-lg);\\n    line-height: var(--tw-leading, var(--text-lg--line-height));\\n  }\\n  .text-sm {\\n    font-size: var(--text-sm);\\n    line-height: var(--tw-leading, var(--text-sm--line-height));\\n  }\\n  .text-xs {\\n    font-size: var(--text-xs);\\n    line-height: var(--tw-leading, var(--text-xs--line-height));\\n  }\\n  .leading-none {\\n    --tw-leading: 1;\\n    line-height: 1;\\n  }\\n  .leading-tight {\\n    --tw-leading: var(--leading-tight);\\n    line-height: var(--leading-tight);\\n  }\\n  .font-bold {\\n    --tw-font-weight: var(--font-weight-bold);\\n    font-weight: var(--font-weight-bold);\\n  }\\n  .font-extrabold {\\n    --tw-font-weight: var(--font-weight-extrabold);\\n    font-weight: var(--font-weight-extrabold);\\n  }\\n  .font-medium {\\n    --tw-font-weight: var(--font-weight-medium);\\n    font-weight: var(--font-weight-medium);\\n  }\\n  .font-normal {\\n    --tw-font-weight: var(--font-weight-normal);\\n    font-weight: var(--font-weight-normal);\\n  }\\n  .font-semibold {\\n    --tw-font-weight: var(--font-weight-semibold);\\n    font-weight: var(--font-weight-semibold);\\n  }\\n  .tracking-tight {\\n    --tw-tracking: var(--tracking-tight);\\n    letter-spacing: var(--tracking-tight);\\n  }\\n  .tracking-widest {\\n    --tw-tracking: var(--tracking-widest);\\n    letter-spacing: var(--tracking-widest);\\n  }\\n  .text-balance {\\n    text-wrap: balance;\\n  }\\n  .break-words {\\n    overflow-wrap: break-word;\\n  }\\n  .whitespace-nowrap {\\n    white-space: nowrap;\\n  }\\n  .text-card-foreground {\\n    color: var(--card-foreground);\\n  }\\n  .text-dccblue {\\n    color: var(--dccblue);\\n  }\\n  .text-foreground {\\n    color: var(--foreground);\\n  }\\n  .text-gray-700 {\\n    color: var(--color-gray-700);\\n  }\\n  .text-gray-900 {\\n    color: var(--color-gray-900);\\n  }\\n  .text-muted-foreground {\\n    color: var(--muted-foreground);\\n  }\\n  .text-popover-foreground {\\n    color: var(--popover-foreground);\\n  }\\n  .text-primary {\\n    color: var(--primary);\\n  }\\n  .text-primary-foreground {\\n    color: var(--primary-foreground);\\n  }\\n  .text-secondary-foreground {\\n    color: var(--secondary-foreground);\\n  }\\n  .text-sidebar-foreground {\\n    color: var(--sidebar-foreground);\\n  }\\n  .text-sidebar-foreground\\\\/70 {\\n    color: var(--sidebar-foreground);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);\\n    }\\n  }\\n  .text-sidebar-primary-foreground {\\n    color: var(--sidebar-primary-foreground);\\n  }\\n  .text-slate-700 {\\n    color: var(--color-slate-700);\\n  }\\n  .text-white {\\n    color: var(--color-white);\\n  }\\n  .tabular-nums {\\n    --tw-numeric-spacing: tabular-nums;\\n    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);\\n  }\\n  .underline-offset-4 {\\n    text-underline-offset: 4px;\\n  }\\n  .antialiased {\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n  .placeholder-gray-400 {\\n    &::placeholder {\\n      color: var(--color-gray-400);\\n    }\\n  }\\n  .opacity-50 {\\n    opacity: 50%;\\n  }\\n  .opacity-70 {\\n    opacity: 70%;\\n  }\\n  .shadow-\\\\[0_0_0_1px_hsl\\\\(var\\\\(--sidebar-border\\\\)\\\\)\\\\] {\\n    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-lg {\\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-md {\\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-none {\\n    --tw-shadow: 0 0 #0000;\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-sm {\\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-xs {\\n    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .ring-sidebar-ring {\\n    --tw-ring-color: var(--sidebar-ring);\\n  }\\n  .ring-offset-background {\\n    --tw-ring-offset-color: var(--background);\\n  }\\n  .outline-hidden {\\n    --tw-outline-style: none;\\n    outline-style: none;\\n    @media (forced-colors: active) {\\n      outline: 2px solid transparent;\\n      outline-offset: 2px;\\n    }\\n  }\\n  .outline {\\n    outline-style: var(--tw-outline-style);\\n    outline-width: 1px;\\n  }\\n  .transition {\\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[color\\\\,box-shadow\\\\] {\\n    transition-property: color,box-shadow;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[left\\\\,right\\\\,width\\\\] {\\n    transition-property: left,right,width;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[margin\\\\,opacity\\\\] {\\n    transition-property: margin,opacity;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[width\\\\,height\\\\,padding\\\\] {\\n    transition-property: width,height,padding;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[width\\\\] {\\n    transition-property: width;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-all {\\n    transition-property: all;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-colors {\\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-opacity {\\n    transition-property: opacity;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-transform {\\n    transition-property: transform, translate, scale, rotate;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .duration-200 {\\n    --tw-duration: 200ms;\\n    transition-duration: 200ms;\\n  }\\n  .duration-300 {\\n    --tw-duration: 300ms;\\n    transition-duration: 300ms;\\n  }\\n  .ease-in-out {\\n    --tw-ease: var(--ease-in-out);\\n    transition-timing-function: var(--ease-in-out);\\n  }\\n  .ease-linear {\\n    --tw-ease: linear;\\n    transition-timing-function: linear;\\n  }\\n  .fade-in-0 {\\n    --tw-enter-opacity: calc(0/100);\\n    --tw-enter-opacity: 0;\\n  }\\n  .outline-none {\\n    --tw-outline-style: none;\\n    outline-style: none;\\n  }\\n  .select-none {\\n    -webkit-user-select: none;\\n    user-select: none;\\n  }\\n  .zoom-in-95 {\\n    --tw-enter-scale: calc(95*1%);\\n    --tw-enter-scale: .95;\\n  }\\n  .group-focus-within\\\\/menu-item\\\\:opacity-100 {\\n    &:is(:where(.group\\\\/menu-item):focus-within *) {\\n      opacity: 100%;\\n    }\\n  }\\n  .group-hover\\\\/menu-item\\\\:opacity-100 {\\n    &:is(:where(.group\\\\/menu-item):hover *) {\\n      @media (hover: hover) {\\n        opacity: 100%;\\n      }\\n    }\\n  }\\n  .group-has-data-\\\\[sidebar\\\\=menu-action\\\\]\\\\/menu-item\\\\:pr-8 {\\n    &:is(:where(.group\\\\/menu-item):has(*[data-sidebar=\\\"menu-action\\\"]) *) {\\n      padding-right: calc(var(--spacing) * 8);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:-mt-8 {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      margin-top: calc(var(--spacing) * -8);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:hidden {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      display: none;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:size-8\\\\! {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      width: calc(var(--spacing) * 8) !important;\\n      height: calc(var(--spacing) * 8) !important;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\(--sidebar-width-icon\\\\) {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      width: var(--sidebar-width-icon);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[calc\\\\(var\\\\(--sidebar-width-icon\\\\)\\\\+\\\\(--spacing\\\\(4\\\\)\\\\)\\\\)\\\\] {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)));\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[calc\\\\(var\\\\(--sidebar-width-icon\\\\)\\\\+\\\\(--spacing\\\\(4\\\\)\\\\)\\\\+2px\\\\)\\\\] {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)) + 2px);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:overflow-hidden {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      overflow: hidden;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:p-0\\\\! {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      padding: calc(var(--spacing) * 0) !important;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:p-2\\\\! {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      padding: calc(var(--spacing) * 2) !important;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:opacity-0 {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      opacity: 0%;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:right-\\\\[calc\\\\(var\\\\(--sidebar-width\\\\)\\\\*-1\\\\)\\\\] {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      right: calc(var(--sidebar-width) * -1);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:left-\\\\[calc\\\\(var\\\\(--sidebar-width\\\\)\\\\*-1\\\\)\\\\] {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      left: calc(var(--sidebar-width) * -1);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:w-0 {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      width: calc(var(--spacing) * 0);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:translate-x-0 {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      --tw-translate-x: calc(var(--spacing) * 0);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=left\\\\]\\\\:-right-4 {\\n    &:is(:where(.group)[data-side=\\\"left\\\"] *) {\\n      right: calc(var(--spacing) * -4);\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=left\\\\]\\\\:border-r {\\n    &:is(:where(.group)[data-side=\\\"left\\\"] *) {\\n      border-right-style: var(--tw-border-style);\\n      border-right-width: 1px;\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=right\\\\]\\\\:left-0 {\\n    &:is(:where(.group)[data-side=\\\"right\\\"] *) {\\n      left: calc(var(--spacing) * 0);\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=right\\\\]\\\\:rotate-180 {\\n    &:is(:where(.group)[data-side=\\\"right\\\"] *) {\\n      rotate: 180deg;\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=right\\\\]\\\\:border-l {\\n    &:is(:where(.group)[data-side=\\\"right\\\"] *) {\\n      border-left-style: var(--tw-border-style);\\n      border-left-width: 1px;\\n    }\\n  }\\n  .group-data-\\\\[state\\\\=open\\\\]\\\\/collapsible\\\\:rotate-90 {\\n    &:is(:where(.group\\\\/collapsible)[data-state=\\\"open\\\"] *) {\\n      rotate: 90deg;\\n    }\\n  }\\n  .group-data-\\\\[variant\\\\=floating\\\\]\\\\:rounded-lg {\\n    &:is(:where(.group)[data-variant=\\\"floating\\\"] *) {\\n      border-radius: var(--radius);\\n    }\\n  }\\n  .group-data-\\\\[variant\\\\=floating\\\\]\\\\:border {\\n    &:is(:where(.group)[data-variant=\\\"floating\\\"] *) {\\n      border-style: var(--tw-border-style);\\n      border-width: 1px;\\n    }\\n  }\\n  .group-data-\\\\[variant\\\\=floating\\\\]\\\\:border-sidebar-border {\\n    &:is(:where(.group)[data-variant=\\\"floating\\\"] *) {\\n      border-color: var(--sidebar-border);\\n    }\\n  }\\n  .group-data-\\\\[variant\\\\=floating\\\\]\\\\:shadow-sm {\\n    &:is(:where(.group)[data-variant=\\\"floating\\\"] *) {\\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .peer-hover\\\\/menu-button\\\\:text-sidebar-accent-foreground {\\n    &:is(:where(.peer\\\\/menu-button):hover ~ *) {\\n      @media (hover: hover) {\\n        color: var(--sidebar-accent-foreground);\\n      }\\n    }\\n  }\\n  .peer-data-\\\\[active\\\\=true\\\\]\\\\/menu-button\\\\:text-sidebar-accent-foreground {\\n    &:is(:where(.peer\\\\/menu-button)[data-active=\\\"true\\\"] ~ *) {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .peer-data-\\\\[size\\\\=default\\\\]\\\\/menu-button\\\\:top-1\\\\.5 {\\n    &:is(:where(.peer\\\\/menu-button)[data-size=\\\"default\\\"] ~ *) {\\n      top: calc(var(--spacing) * 1.5);\\n    }\\n  }\\n  .peer-data-\\\\[size\\\\=lg\\\\]\\\\/menu-button\\\\:top-2\\\\.5 {\\n    &:is(:where(.peer\\\\/menu-button)[data-size=\\\"lg\\\"] ~ *) {\\n      top: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .peer-data-\\\\[size\\\\=sm\\\\]\\\\/menu-button\\\\:top-1 {\\n    &:is(:where(.peer\\\\/menu-button)[data-size=\\\"sm\\\"] ~ *) {\\n      top: calc(var(--spacing) * 1);\\n    }\\n  }\\n  .selection\\\\:bg-primary {\\n    & *::selection {\\n      background-color: var(--primary);\\n    }\\n    &::selection {\\n      background-color: var(--primary);\\n    }\\n  }\\n  .selection\\\\:text-primary-foreground {\\n    & *::selection {\\n      color: var(--primary-foreground);\\n    }\\n    &::selection {\\n      color: var(--primary-foreground);\\n    }\\n  }\\n  .file\\\\:inline-flex {\\n    &::file-selector-button {\\n      display: inline-flex;\\n    }\\n  }\\n  .file\\\\:h-7 {\\n    &::file-selector-button {\\n      height: calc(var(--spacing) * 7);\\n    }\\n  }\\n  .file\\\\:border-0 {\\n    &::file-selector-button {\\n      border-style: var(--tw-border-style);\\n      border-width: 0px;\\n    }\\n  }\\n  .file\\\\:bg-transparent {\\n    &::file-selector-button {\\n      background-color: transparent;\\n    }\\n  }\\n  .file\\\\:text-sm {\\n    &::file-selector-button {\\n      font-size: var(--text-sm);\\n      line-height: var(--tw-leading, var(--text-sm--line-height));\\n    }\\n  }\\n  .file\\\\:font-medium {\\n    &::file-selector-button {\\n      --tw-font-weight: var(--font-weight-medium);\\n      font-weight: var(--font-weight-medium);\\n    }\\n  }\\n  .file\\\\:text-foreground {\\n    &::file-selector-button {\\n      color: var(--foreground);\\n    }\\n  }\\n  .placeholder\\\\:text-muted-foreground {\\n    &::placeholder {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .placeholder\\\\:text-slate-400 {\\n    &::placeholder {\\n      color: var(--color-slate-400);\\n    }\\n  }\\n  .after\\\\:absolute {\\n    &::after {\\n      content: var(--tw-content);\\n      position: absolute;\\n    }\\n  }\\n  .after\\\\:-inset-2 {\\n    &::after {\\n      content: var(--tw-content);\\n      inset: calc(var(--spacing) * -2);\\n    }\\n  }\\n  .after\\\\:inset-y-0 {\\n    &::after {\\n      content: var(--tw-content);\\n      inset-block: calc(var(--spacing) * 0);\\n    }\\n  }\\n  .after\\\\:left-1\\\\/2 {\\n    &::after {\\n      content: var(--tw-content);\\n      left: calc(1/2 * 100%);\\n    }\\n  }\\n  .after\\\\:w-\\\\[2px\\\\] {\\n    &::after {\\n      content: var(--tw-content);\\n      width: 2px;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:after\\\\:left-full {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      &::after {\\n        content: var(--tw-content);\\n        left: 100%;\\n      }\\n    }\\n  }\\n  .last\\\\:border-b-0 {\\n    &:last-child {\\n      border-bottom-style: var(--tw-border-style);\\n      border-bottom-width: 0px;\\n    }\\n  }\\n  .hover\\\\:border-slate-300 {\\n    &:hover {\\n      @media (hover: hover) {\\n        border-color: var(--color-slate-300);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-accent {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--accent);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-destructive\\\\/90 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--destructive);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-green-700 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--color-green-700);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-muted\\\\/50 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--muted);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--muted) 50%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-primary {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--primary);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-primary\\\\/90 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--primary);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--primary) 90%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-secondary\\\\/80 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--secondary);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--secondary) 80%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-sidebar-accent {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--sidebar-accent);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-white\\\\/10 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: color-mix(in srgb, #fff 10%, transparent);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:text-accent-foreground {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--accent-foreground);\\n      }\\n    }\\n  }\\n  .hover\\\\:text-foreground {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--foreground);\\n      }\\n    }\\n  }\\n  .hover\\\\:text-sidebar-accent-foreground {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--sidebar-accent-foreground);\\n      }\\n    }\\n  }\\n  .hover\\\\:text-white {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--color-white);\\n      }\\n    }\\n  }\\n  .hover\\\\:underline {\\n    &:hover {\\n      @media (hover: hover) {\\n        text-decoration-line: underline;\\n      }\\n    }\\n  }\\n  .hover\\\\:opacity-100 {\\n    &:hover {\\n      @media (hover: hover) {\\n        opacity: 100%;\\n      }\\n    }\\n  }\\n  .hover\\\\:shadow {\\n    &:hover {\\n      @media (hover: hover) {\\n        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n      }\\n    }\\n  }\\n  .hover\\\\:shadow-\\\\[0_0_0_1px_hsl\\\\(var\\\\(--sidebar-accent\\\\)\\\\)\\\\] {\\n    &:hover {\\n      @media (hover: hover) {\\n        --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));\\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n      }\\n    }\\n  }\\n  .hover\\\\:group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:bg-sidebar {\\n    &:hover {\\n      @media (hover: hover) {\\n        &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n          background-color: var(--sidebar);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:after\\\\:bg-sidebar-border {\\n    &:hover {\\n      @media (hover: hover) {\\n        &::after {\\n          content: var(--tw-content);\\n          background-color: var(--sidebar-border);\\n        }\\n      }\\n    }\\n  }\\n  .focus\\\\:border-primary {\\n    &:focus {\\n      border-color: var(--primary);\\n    }\\n  }\\n  .focus\\\\:border-slate-400 {\\n    &:focus {\\n      border-color: var(--color-slate-400);\\n    }\\n  }\\n  .focus\\\\:bg-accent {\\n    &:focus {\\n      background-color: var(--accent);\\n    }\\n  }\\n  .focus\\\\:bg-green-500 {\\n    &:focus {\\n      background-color: var(--color-green-500);\\n    }\\n  }\\n  .focus\\\\:text-accent-foreground {\\n    &:focus {\\n      color: var(--accent-foreground);\\n    }\\n  }\\n  .focus\\\\:shadow {\\n    &:focus {\\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus\\\\:shadow-none {\\n    &:focus {\\n      --tw-shadow: 0 0 #0000;\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus\\\\:ring-2 {\\n    &:focus {\\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus\\\\:ring-indigo-500 {\\n    &:focus {\\n      --tw-ring-color: var(--color-indigo-500);\\n    }\\n  }\\n  .focus\\\\:ring-primary {\\n    &:focus {\\n      --tw-ring-color: var(--primary);\\n    }\\n  }\\n  .focus\\\\:ring-ring {\\n    &:focus {\\n      --tw-ring-color: var(--ring);\\n    }\\n  }\\n  .focus\\\\:ring-offset-2 {\\n    &:focus {\\n      --tw-ring-offset-width: 2px;\\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n    }\\n  }\\n  .focus\\\\:outline-hidden {\\n    &:focus {\\n      --tw-outline-style: none;\\n      outline-style: none;\\n      @media (forced-colors: active) {\\n        outline: 2px solid transparent;\\n        outline-offset: 2px;\\n      }\\n    }\\n  }\\n  .focus\\\\:outline-none {\\n    &:focus {\\n      --tw-outline-style: none;\\n      outline-style: none;\\n    }\\n  }\\n  .focus-visible\\\\:border-ring {\\n    &:focus-visible {\\n      border-color: var(--ring);\\n    }\\n  }\\n  .focus-visible\\\\:ring-2 {\\n    &:focus-visible {\\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus-visible\\\\:ring-\\\\[3px\\\\] {\\n    &:focus-visible {\\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus-visible\\\\:ring-destructive\\\\/20 {\\n    &:focus-visible {\\n      --tw-ring-color: var(--destructive);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\\n      }\\n    }\\n  }\\n  .focus-visible\\\\:ring-ring\\\\/50 {\\n    &:focus-visible {\\n      --tw-ring-color: var(--ring);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\\n      }\\n    }\\n  }\\n  .focus-visible\\\\:outline-1 {\\n    &:focus-visible {\\n      outline-style: var(--tw-outline-style);\\n      outline-width: 1px;\\n    }\\n  }\\n  .focus-visible\\\\:outline-ring {\\n    &:focus-visible {\\n      outline-color: var(--ring);\\n    }\\n  }\\n  .active\\\\:bg-green-700 {\\n    &:active {\\n      background-color: var(--color-green-700);\\n    }\\n  }\\n  .active\\\\:bg-sidebar-accent {\\n    &:active {\\n      background-color: var(--sidebar-accent);\\n    }\\n  }\\n  .active\\\\:bg-white\\\\/10 {\\n    &:active {\\n      background-color: color-mix(in srgb, #fff 10%, transparent);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\\n      }\\n    }\\n  }\\n  .active\\\\:text-sidebar-accent-foreground {\\n    &:active {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .active\\\\:shadow-none {\\n    &:active {\\n      --tw-shadow: 0 0 #0000;\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .disabled\\\\:pointer-events-none {\\n    &:disabled {\\n      pointer-events: none;\\n    }\\n  }\\n  .disabled\\\\:cursor-not-allowed {\\n    &:disabled {\\n      cursor: not-allowed;\\n    }\\n  }\\n  .disabled\\\\:opacity-50 {\\n    &:disabled {\\n      opacity: 50%;\\n    }\\n  }\\n  .disabled\\\\:shadow-none {\\n    &:disabled {\\n      --tw-shadow: 0 0 #0000;\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .in-data-\\\\[side\\\\=left\\\\]\\\\:cursor-w-resize {\\n    :where(*[data-side=\\\"left\\\"]) & {\\n      cursor: w-resize;\\n    }\\n  }\\n  .in-data-\\\\[side\\\\=right\\\\]\\\\:cursor-e-resize {\\n    :where(*[data-side=\\\"right\\\"]) & {\\n      cursor: e-resize;\\n    }\\n  }\\n  .has-data-\\\\[slot\\\\=card-action\\\\]\\\\:grid-cols-\\\\[1fr_auto\\\\] {\\n    &:has(*[data-slot=\\\"card-action\\\"]) {\\n      grid-template-columns: 1fr auto;\\n    }\\n  }\\n  .has-data-\\\\[variant\\\\=inset\\\\]\\\\:bg-sidebar {\\n    &:has(*[data-variant=\\\"inset\\\"]) {\\n      background-color: var(--sidebar);\\n    }\\n  }\\n  .has-\\\\[\\\\>svg\\\\]\\\\:px-2\\\\.5 {\\n    &:has(>svg) {\\n      padding-inline: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .has-\\\\[\\\\>svg\\\\]\\\\:px-3 {\\n    &:has(>svg) {\\n      padding-inline: calc(var(--spacing) * 3);\\n    }\\n  }\\n  .has-\\\\[\\\\>svg\\\\]\\\\:px-4 {\\n    &:has(>svg) {\\n      padding-inline: calc(var(--spacing) * 4);\\n    }\\n  }\\n  .aria-disabled\\\\:pointer-events-none {\\n    &[aria-disabled=\\\"true\\\"] {\\n      pointer-events: none;\\n    }\\n  }\\n  .aria-disabled\\\\:opacity-50 {\\n    &[aria-disabled=\\\"true\\\"] {\\n      opacity: 50%;\\n    }\\n  }\\n  .aria-invalid\\\\:border-destructive {\\n    &[aria-invalid=\\\"true\\\"] {\\n      border-color: var(--destructive);\\n    }\\n  }\\n  .aria-invalid\\\\:ring-destructive\\\\/20 {\\n    &[aria-invalid=\\\"true\\\"] {\\n      --tw-ring-color: var(--destructive);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\\n      }\\n    }\\n  }\\n  .data-\\\\[active\\\\=true\\\\]\\\\:bg-sidebar-accent {\\n    &[data-active=\\\"true\\\"] {\\n      background-color: var(--sidebar-accent);\\n    }\\n  }\\n  .data-\\\\[active\\\\=true\\\\]\\\\:font-medium {\\n    &[data-active=\\\"true\\\"] {\\n      --tw-font-weight: var(--font-weight-medium);\\n      font-weight: var(--font-weight-medium);\\n    }\\n  }\\n  .data-\\\\[active\\\\=true\\\\]\\\\:text-sidebar-accent-foreground {\\n    &[data-active=\\\"true\\\"] {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .data-\\\\[disabled\\\\]\\\\:pointer-events-none {\\n    &[data-disabled] {\\n      pointer-events: none;\\n    }\\n  }\\n  .data-\\\\[disabled\\\\]\\\\:opacity-50 {\\n    &[data-disabled] {\\n      opacity: 50%;\\n    }\\n  }\\n  .data-\\\\[inset\\\\]\\\\:pl-8 {\\n    &[data-inset] {\\n      padding-left: calc(var(--spacing) * 8);\\n    }\\n  }\\n  .data-\\\\[orientation\\\\=horizontal\\\\]\\\\:h-px {\\n    &[data-orientation=\\\"horizontal\\\"] {\\n      height: 1px;\\n    }\\n  }\\n  .data-\\\\[orientation\\\\=horizontal\\\\]\\\\:w-full {\\n    &[data-orientation=\\\"horizontal\\\"] {\\n      width: 100%;\\n    }\\n  }\\n  .data-\\\\[orientation\\\\=vertical\\\\]\\\\:h-full {\\n    &[data-orientation=\\\"vertical\\\"] {\\n      height: 100%;\\n    }\\n  }\\n  .data-\\\\[orientation\\\\=vertical\\\\]\\\\:w-px {\\n    &[data-orientation=\\\"vertical\\\"] {\\n      width: 1px;\\n    }\\n  }\\n  .data-\\\\[placeholder\\\\]\\\\:text-muted-foreground {\\n    &[data-placeholder] {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .data-\\\\[side\\\\=bottom\\\\]\\\\:translate-y-1 {\\n    &[data-side=\\\"bottom\\\"] {\\n      --tw-translate-y: calc(var(--spacing) * 1);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .data-\\\\[side\\\\=bottom\\\\]\\\\:slide-in-from-top-2 {\\n    &[data-side=\\\"bottom\\\"] {\\n      --tw-enter-translate-y: calc(2*var(--spacing)*-1);\\n    }\\n  }\\n  .data-\\\\[side\\\\=left\\\\]\\\\:-translate-x-1 {\\n    &[data-side=\\\"left\\\"] {\\n      --tw-translate-x: calc(var(--spacing) * -1);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .data-\\\\[side\\\\=left\\\\]\\\\:slide-in-from-right-2 {\\n    &[data-side=\\\"left\\\"] {\\n      --tw-enter-translate-x: calc(2*var(--spacing));\\n    }\\n  }\\n  .data-\\\\[side\\\\=right\\\\]\\\\:translate-x-1 {\\n    &[data-side=\\\"right\\\"] {\\n      --tw-translate-x: calc(var(--spacing) * 1);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .data-\\\\[side\\\\=right\\\\]\\\\:slide-in-from-left-2 {\\n    &[data-side=\\\"right\\\"] {\\n      --tw-enter-translate-x: calc(2*var(--spacing)*-1);\\n    }\\n  }\\n  .data-\\\\[side\\\\=top\\\\]\\\\:-translate-y-1 {\\n    &[data-side=\\\"top\\\"] {\\n      --tw-translate-y: calc(var(--spacing) * -1);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .data-\\\\[side\\\\=top\\\\]\\\\:slide-in-from-bottom-2 {\\n    &[data-side=\\\"top\\\"] {\\n      --tw-enter-translate-y: calc(2*var(--spacing));\\n    }\\n  }\\n  .data-\\\\[size\\\\=default\\\\]\\\\:h-9 {\\n    &[data-size=\\\"default\\\"] {\\n      height: calc(var(--spacing) * 9);\\n    }\\n  }\\n  .data-\\\\[size\\\\=sm\\\\]\\\\:h-8 {\\n    &[data-size=\\\"sm\\\"] {\\n      height: calc(var(--spacing) * 8);\\n    }\\n  }\\n  .\\\\*\\\\:data-\\\\[slot\\\\=select-value\\\\]\\\\:line-clamp-1 {\\n    :is(& > *) {\\n      &[data-slot=\\\"select-value\\\"] {\\n        overflow: hidden;\\n        display: -webkit-box;\\n        -webkit-box-orient: vertical;\\n        -webkit-line-clamp: 1;\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:data-\\\\[slot\\\\=select-value\\\\]\\\\:flex {\\n    :is(& > *) {\\n      &[data-slot=\\\"select-value\\\"] {\\n        display: flex;\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:data-\\\\[slot\\\\=select-value\\\\]\\\\:items-center {\\n    :is(& > *) {\\n      &[data-slot=\\\"select-value\\\"] {\\n        align-items: center;\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:data-\\\\[slot\\\\=select-value\\\\]\\\\:gap-2 {\\n    :is(& > *) {\\n      &[data-slot=\\\"select-value\\\"] {\\n        gap: calc(var(--spacing) * 2);\\n      }\\n    }\\n  }\\n  .data-\\\\[slot\\\\=sidebar-menu-button\\\\]\\\\:\\\\!p-1\\\\.5 {\\n    &[data-slot=\\\"sidebar-menu-button\\\"] {\\n      padding: calc(var(--spacing) * 1.5) !important;\\n    }\\n  }\\n  .data-\\\\[state\\\\=active\\\\]\\\\:shadow-sm {\\n    &[data-state=\\\"active\\\"] {\\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:animate-accordion-up {\\n    &[data-state=\\\"closed\\\"] {\\n      animation: accordion-up var(--tw-animation-duration,var(--tw-duration,.2s))ease-out;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:animate-out {\\n    &[data-state=\\\"closed\\\"] {\\n      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:duration-300 {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-duration: 300ms;\\n      transition-duration: 300ms;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-0 {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-opacity: calc(0/100);\\n      --tw-exit-opacity: 0;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:zoom-out-95 {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-scale: calc(95*1%);\\n      --tw-exit-scale: .95;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-bottom {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-translate-y: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-translate-x: -100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-right {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-translate-x: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-translate-y: -100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:animate-accordion-down {\\n    &[data-state=\\\"open\\\"] {\\n      animation: accordion-down var(--tw-animation-duration,var(--tw-duration,.2s))ease-out;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:animate-in {\\n    &[data-state=\\\"open\\\"] {\\n      animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:bg-accent {\\n    &[data-state=\\\"open\\\"] {\\n      background-color: var(--accent);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:bg-secondary {\\n    &[data-state=\\\"open\\\"] {\\n      background-color: var(--secondary);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:bg-sidebar-accent {\\n    &[data-state=\\\"open\\\"] {\\n      background-color: var(--sidebar-accent);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:text-accent-foreground {\\n    &[data-state=\\\"open\\\"] {\\n      color: var(--accent-foreground);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:text-sidebar-accent-foreground {\\n    &[data-state=\\\"open\\\"] {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:opacity-100 {\\n    &[data-state=\\\"open\\\"] {\\n      opacity: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:duration-500 {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-duration: 500ms;\\n      transition-duration: 500ms;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:fade-in-0 {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-opacity: calc(0/100);\\n      --tw-enter-opacity: 0;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-95 {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-scale: calc(95*1%);\\n      --tw-enter-scale: .95;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-bottom {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-translate-y: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-translate-x: -100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-right {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-translate-x: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-translate-y: -100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:hover\\\\:bg-sidebar-accent {\\n    &[data-state=\\\"open\\\"] {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--sidebar-accent);\\n        }\\n      }\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:hover\\\\:text-sidebar-accent-foreground {\\n    &[data-state=\\\"open\\\"] {\\n      &:hover {\\n        @media (hover: hover) {\\n          color: var(--sidebar-accent-foreground);\\n        }\\n      }\\n    }\\n  }\\n  .data-\\\\[state\\\\=selected\\\\]\\\\:bg-muted {\\n    &[data-state=\\\"selected\\\"] {\\n      background-color: var(--muted);\\n    }\\n  }\\n  .data-\\\\[variant\\\\=destructive\\\\]\\\\:text-destructive {\\n    &[data-variant=\\\"destructive\\\"] {\\n      color: var(--destructive);\\n    }\\n  }\\n  .data-\\\\[variant\\\\=destructive\\\\]\\\\:focus\\\\:bg-destructive\\\\/10 {\\n    &[data-variant=\\\"destructive\\\"] {\\n      &:focus {\\n        background-color: var(--destructive);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--destructive) 10%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .data-\\\\[variant\\\\=destructive\\\\]\\\\:focus\\\\:text-destructive {\\n    &[data-variant=\\\"destructive\\\"] {\\n      &:focus {\\n        color: var(--destructive);\\n      }\\n    }\\n  }\\n  .sm\\\\:mx-auto {\\n    @media (width >= 40rem) {\\n      margin-inline: auto;\\n    }\\n  }\\n  .sm\\\\:block {\\n    @media (width >= 40rem) {\\n      display: block;\\n    }\\n  }\\n  .sm\\\\:flex {\\n    @media (width >= 40rem) {\\n      display: flex;\\n    }\\n  }\\n  .sm\\\\:w-full {\\n    @media (width >= 40rem) {\\n      width: 100%;\\n    }\\n  }\\n  .sm\\\\:max-w-md {\\n    @media (width >= 40rem) {\\n      max-width: var(--container-md);\\n    }\\n  }\\n  .sm\\\\:max-w-sm {\\n    @media (width >= 40rem) {\\n      max-width: var(--container-sm);\\n    }\\n  }\\n  .sm\\\\:gap-2\\\\.5 {\\n    @media (width >= 40rem) {\\n      gap: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .sm\\\\:px-6 {\\n    @media (width >= 40rem) {\\n      padding-inline: calc(var(--spacing) * 6);\\n    }\\n  }\\n  .sm\\\\:pr-2\\\\.5 {\\n    @media (width >= 40rem) {\\n      padding-right: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .sm\\\\:pl-2\\\\.5 {\\n    @media (width >= 40rem) {\\n      padding-left: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .sm\\\\:text-sm {\\n    @media (width >= 40rem) {\\n      font-size: var(--text-sm);\\n      line-height: var(--tw-leading, var(--text-sm--line-height));\\n    }\\n  }\\n  .md\\\\:block {\\n    @media (width >= 48rem) {\\n      display: block;\\n    }\\n  }\\n  .md\\\\:flex {\\n    @media (width >= 48rem) {\\n      display: flex;\\n    }\\n  }\\n  .md\\\\:text-sm {\\n    @media (width >= 48rem) {\\n      font-size: var(--text-sm);\\n      line-height: var(--tw-leading, var(--text-sm--line-height));\\n    }\\n  }\\n  .md\\\\:opacity-0 {\\n    @media (width >= 48rem) {\\n      opacity: 0%;\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:m-2 {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        margin: calc(var(--spacing) * 2);\\n      }\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:ml-0 {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        margin-left: calc(var(--spacing) * 0);\\n      }\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:rounded-xl {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        border-radius: calc(var(--radius) + 4px);\\n      }\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:shadow-sm {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n      }\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:peer-data-\\\\[state\\\\=collapsed\\\\]\\\\:ml-2 {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        &:is(:where(.peer)[data-state=\\\"collapsed\\\"] ~ *) {\\n          margin-left: calc(var(--spacing) * 2);\\n        }\\n      }\\n    }\\n  }\\n  .md\\\\:after\\\\:hidden {\\n    @media (width >= 48rem) {\\n      &::after {\\n        content: var(--tw-content);\\n        display: none;\\n      }\\n    }\\n  }\\n  .lg\\\\:px-8 {\\n    @media (width >= 64rem) {\\n      padding-inline: calc(var(--spacing) * 8);\\n    }\\n  }\\n  .dark\\\\:border-input {\\n    &:is(.dark *) {\\n      border-color: var(--input);\\n    }\\n  }\\n  .dark\\\\:bg-destructive\\\\/60 {\\n    &:is(.dark *) {\\n      background-color: var(--destructive);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        background-color: color-mix(in oklab, var(--destructive) 60%, transparent);\\n      }\\n    }\\n  }\\n  .dark\\\\:bg-input\\\\/30 {\\n    &:is(.dark *) {\\n      background-color: var(--input);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        background-color: color-mix(in oklab, var(--input) 30%, transparent);\\n      }\\n    }\\n  }\\n  .dark\\\\:text-muted-foreground {\\n    &:is(.dark *) {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .dark\\\\:hover\\\\:bg-accent\\\\/50 {\\n    &:is(.dark *) {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--accent);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--accent) 50%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:hover\\\\:bg-input\\\\/50 {\\n    &:is(.dark *) {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--input);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--input) 50%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:focus-visible\\\\:ring-destructive\\\\/40 {\\n    &:is(.dark *) {\\n      &:focus-visible {\\n        --tw-ring-color: var(--destructive);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:aria-invalid\\\\:ring-destructive\\\\/40 {\\n    &:is(.dark *) {\\n      &[aria-invalid=\\\"true\\\"] {\\n        --tw-ring-color: var(--destructive);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[state\\\\=active\\\\]\\\\:border-input {\\n    &:is(.dark *) {\\n      &[data-state=\\\"active\\\"] {\\n        border-color: var(--input);\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[state\\\\=active\\\\]\\\\:bg-input\\\\/30 {\\n    &:is(.dark *) {\\n      &[data-state=\\\"active\\\"] {\\n        background-color: var(--input);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--input) 30%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[state\\\\=active\\\\]\\\\:text-foreground {\\n    &:is(.dark *) {\\n      &[data-state=\\\"active\\\"] {\\n        color: var(--foreground);\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[variant\\\\=destructive\\\\]\\\\:focus\\\\:bg-destructive\\\\/20 {\\n    &:is(.dark *) {\\n      &[data-variant=\\\"destructive\\\"] {\\n        &:focus {\\n          background-color: var(--destructive);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--destructive) 20%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[\\\\&_svg\\\\]\\\\:pointer-events-none {\\n    & svg {\\n      pointer-events: none;\\n    }\\n  }\\n  .\\\\[\\\\&_svg\\\\]\\\\:shrink-0 {\\n    & svg {\\n      flex-shrink: 0;\\n    }\\n  }\\n  .\\\\[\\\\&_svg\\\\:not\\\\(\\\\[class\\\\*\\\\=\\\\'size-\\\\'\\\\]\\\\)\\\\]\\\\:size-4 {\\n    & svg:not([class*='size-']) {\\n      width: calc(var(--spacing) * 4);\\n      height: calc(var(--spacing) * 4);\\n    }\\n  }\\n  .\\\\[\\\\&_svg\\\\:not\\\\(\\\\[class\\\\*\\\\=\\\\'text-\\\\'\\\\]\\\\)\\\\]\\\\:text-muted-foreground {\\n    & svg:not([class*='text-']) {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .\\\\[\\\\&_tr\\\\]\\\\:border-b {\\n    & tr {\\n      border-bottom-style: var(--tw-border-style);\\n      border-bottom-width: 1px;\\n    }\\n  }\\n  .\\\\[\\\\&_tr\\\\:last-child\\\\]\\\\:border-0 {\\n    & tr:last-child {\\n      border-style: var(--tw-border-style);\\n      border-width: 0px;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\:has\\\\(\\\\[role\\\\=checkbox\\\\]\\\\)\\\\]\\\\:pr-0 {\\n    &:has([role=checkbox]) {\\n      padding-right: calc(var(--spacing) * 0);\\n    }\\n  }\\n  .\\\\[\\\\.border-b\\\\]\\\\:pb-3 {\\n    &:is(.border-b) {\\n      padding-bottom: calc(var(--spacing) * 3);\\n    }\\n  }\\n  .\\\\[\\\\.border-t\\\\]\\\\:pt-6 {\\n    &:is(.border-t) {\\n      padding-top: calc(var(--spacing) * 6);\\n    }\\n  }\\n  .\\\\*\\\\:\\\\[span\\\\]\\\\:last\\\\:flex {\\n    :is(& > *) {\\n      &:is(span) {\\n        &:last-child {\\n          display: flex;\\n        }\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:\\\\[span\\\\]\\\\:last\\\\:items-center {\\n    :is(& > *) {\\n      &:is(span) {\\n        &:last-child {\\n          align-items: center;\\n        }\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:\\\\[span\\\\]\\\\:last\\\\:gap-2 {\\n    :is(& > *) {\\n      &:is(span) {\\n        &:last-child {\\n          gap: calc(var(--spacing) * 2);\\n        }\\n      }\\n    }\\n  }\\n  .data-\\\\[variant\\\\=destructive\\\\]\\\\:\\\\*\\\\:\\\\[svg\\\\]\\\\:\\\\!text-destructive {\\n    &[data-variant=\\\"destructive\\\"] {\\n      :is(& > *) {\\n        &:is(svg) {\\n          color: var(--destructive) !important;\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>\\\\[role\\\\=checkbox\\\\]\\\\]\\\\:translate-y-\\\\[2px\\\\] {\\n    &>[role=checkbox] {\\n      --tw-translate-y: 2px;\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>button\\\\]\\\\:hidden {\\n    &>button {\\n      display: none;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>span\\\\:last-child\\\\]\\\\:truncate {\\n    &>span:last-child {\\n      overflow: hidden;\\n      text-overflow: ellipsis;\\n      white-space: nowrap;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:pointer-events-none {\\n    &>svg {\\n      pointer-events: none;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:size-3 {\\n    &>svg {\\n      width: calc(var(--spacing) * 3);\\n      height: calc(var(--spacing) * 3);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:size-3\\\\.5 {\\n    &>svg {\\n      width: calc(var(--spacing) * 3.5);\\n      height: calc(var(--spacing) * 3.5);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:size-4 {\\n    &>svg {\\n      width: calc(var(--spacing) * 4);\\n      height: calc(var(--spacing) * 4);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:shrink-0 {\\n    &>svg {\\n      flex-shrink: 0;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:text-sidebar-accent-foreground {\\n    &>svg {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>tr\\\\]\\\\:last\\\\:border-b-0 {\\n    &>tr {\\n      &:last-child {\\n        border-bottom-style: var(--tw-border-style);\\n        border-bottom-width: 0px;\\n      }\\n    }\\n  }\\n  .\\\\[\\\\&\\\\[data-state\\\\=open\\\\]\\\\>svg\\\\]\\\\:rotate-180 {\\n    &[data-state=open]>svg {\\n      rotate: 180deg;\\n    }\\n  }\\n  .\\\\[\\\\[data-side\\\\=left\\\\]\\\\[data-collapsible\\\\=offcanvas\\\\]_\\\\&\\\\]\\\\:-right-2 {\\n    [data-side=left][data-collapsible=offcanvas] & {\\n      right: calc(var(--spacing) * -2);\\n    }\\n  }\\n  .\\\\[\\\\[data-side\\\\=left\\\\]\\\\[data-state\\\\=collapsed\\\\]_\\\\&\\\\]\\\\:cursor-e-resize {\\n    [data-side=left][data-state=collapsed] & {\\n      cursor: e-resize;\\n    }\\n  }\\n  .\\\\[\\\\[data-side\\\\=right\\\\]\\\\[data-collapsible\\\\=offcanvas\\\\]_\\\\&\\\\]\\\\:-left-2 {\\n    [data-side=right][data-collapsible=offcanvas] & {\\n      left: calc(var(--spacing) * -2);\\n    }\\n  }\\n  .\\\\[\\\\[data-side\\\\=right\\\\]\\\\[data-state\\\\=collapsed\\\\]_\\\\&\\\\]\\\\:cursor-w-resize {\\n    [data-side=right][data-state=collapsed] & {\\n      cursor: w-resize;\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:bg-accent {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--accent);\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:bg-destructive\\\\/90 {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--destructive);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:bg-primary\\\\/90 {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--primary);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--primary) 90%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:bg-secondary\\\\/90 {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--secondary);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--secondary) 90%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:text-accent-foreground {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          color: var(--accent-foreground);\\n        }\\n      }\\n    }\\n  }\\n}\\n@property --tw-animation-delay {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0s;\\n}\\n@property --tw-animation-direction {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: normal;\\n}\\n@property --tw-animation-duration {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-animation-fill-mode {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: none;\\n}\\n@property --tw-animation-iteration-count {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-enter-opacity {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-enter-rotate {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-enter-scale {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-enter-translate-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-enter-translate-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-exit-opacity {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-exit-rotate {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-exit-scale {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-exit-translate-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-exit-translate-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n:root {\\n  --background: #ffffff;\\n  --foreground: #171717;\\n  --card: oklch(1 0 0);\\n  --card-foreground: oklch(0.145 0 0);\\n  --popover: oklch(1 0 0);\\n  --popover-foreground: oklch(0.145 0 0);\\n  --primary: oklch(24.37% 0.0951 286.37);\\n  --primary-foreground: oklch(0.985 0 0);\\n  --secondary: oklch(37.34% 0.1397 315.97);\\n  --secondary-foreground: oklch(0.205 0 0);\\n  --muted: oklch(0.97 0 0);\\n  --muted-foreground: oklch(0.556 0 0);\\n  --accent: oklch(0.97 0 0);\\n  --accent-foreground: oklch(0.205 0 0);\\n  --destructive: oklch(0.577 0.245 27.325);\\n  --destructive-foreground: oklch(0.577 0.245 27.325);\\n  --border: oklch(0.922 0 0);\\n  --input: oklch(0.922 0 0);\\n  --ring: oklch(0.708 0 0);\\n  --chart-1: oklch(0.646 0.222 41.116);\\n  --chart-2: oklch(0.6 0.118 184.704);\\n  --chart-3: oklch(0.398 0.07 227.392);\\n  --chart-4: oklch(0.828 0.189 84.429);\\n  --chart-5: oklch(0.769 0.188 70.08);\\n  --radius: 0.625rem;\\n  --sidebar: oklch(0.985 0 0);\\n  --sidebar-foreground: oklch(0.145 0 0);\\n  --sidebar-primary: oklch(0.205 0 0);\\n  --sidebar-primary-foreground: oklch(0.985 0 0);\\n  --sidebar-accent: oklch(0.97 0 0);\\n  --sidebar-accent-foreground: oklch(0.205 0 0);\\n  --sidebar-border: oklch(0.922 0 0);\\n  --sidebar-ring: oklch(0.708 0 0);\\n  --dccpink: oklch(0.54 0.216689 5.2);\\n  --dccblue: oklch(0.24 0.0951 286.37);\\n  --dcclightblue: oklch(0.64 0.1154 218.6);\\n  --dccviolet: oklch(0.45 0.1883 326.95);\\n  --dccpurple: oklch(0.37 0.1397 315.97);\\n  --dcclightgrey: oklch(0.7 0.0146 134.93);\\n  --dccdarkgrey: oklch(0.44 0.0031 228.84);\\n  --dccyellow: oklch(0.87 0.1768 90.38);\\n  --dccgreen: oklch(0.75 0.1806 124.9);\\n  --dccgrey: oklch(0.7 0.0146 134.93);\\n  --dccorange: oklch(0.75 0.1674 64.79);\\n  --dcclightorange: oklch(0.83 0.1464 73.9);\\n}\\nbody {\\n  background: var(--background);\\n  color: var(--foreground);\\n  font-family: Arial, Helvetica, sans-serif;\\n}\\n.dark {\\n  --background: oklch(0.145 0 0);\\n  --foreground: oklch(0.985 0 0);\\n  --card: oklch(0.145 0 0);\\n  --card-foreground: oklch(0.985 0 0);\\n  --popover: oklch(0.145 0 0);\\n  --popover-foreground: oklch(0.985 0 0);\\n  --primary: oklch(0.985 0 0);\\n  --primary-foreground: oklch(0.205 0 0);\\n  --secondary: oklch(0.269 0 0);\\n  --secondary-foreground: oklch(0.985 0 0);\\n  --muted: oklch(0.269 0 0);\\n  --muted-foreground: oklch(0.708 0 0);\\n  --accent: oklch(0.269 0 0);\\n  --accent-foreground: oklch(0.985 0 0);\\n  --destructive: oklch(0.396 0.141 25.723);\\n  --destructive-foreground: oklch(0.637 0.237 25.331);\\n  --border: oklch(0.269 0 0);\\n  --input: oklch(0.269 0 0);\\n  --ring: oklch(0.439 0 0);\\n  --chart-1: oklch(0.488 0.243 264.376);\\n  --chart-2: oklch(0.696 0.17 162.48);\\n  --chart-3: oklch(0.769 0.188 70.08);\\n  --chart-4: oklch(0.627 0.265 303.9);\\n  --chart-5: oklch(0.645 0.246 16.439);\\n  --sidebar: oklch(0.205 0 0);\\n  --sidebar-foreground: oklch(0.985 0 0);\\n  --sidebar-primary: oklch(0.488 0.243 264.376);\\n  --sidebar-primary-foreground: oklch(0.985 0 0);\\n  --sidebar-accent: oklch(0.269 0 0);\\n  --sidebar-accent-foreground: oklch(0.985 0 0);\\n  --sidebar-border: oklch(0.269 0 0);\\n  --sidebar-ring: oklch(0.439 0 0);\\n}\\n@layer base {\\n  * {\\n    border-color: var(--border);\\n    outline-color: var(--ring);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);\\n    }\\n  }\\n  body {\\n    background-color: var(--background);\\n    color: var(--foreground);\\n  }\\n}\\n@layer base {\\n  :root {\\n    --sidebar-background: 0 0% 98%;\\n    --sidebar-foreground: 240 5.3% 26.1%;\\n    --sidebar-primary: 240 5.9% 10%;\\n    --sidebar-primary-foreground: 0 0% 98%;\\n    --sidebar-accent: 240 4.8% 95.9%;\\n    --sidebar-accent-foreground: 240 5.9% 10%;\\n    --sidebar-border: 220 13% 91%;\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\n  }\\n  .dark {\\n    --sidebar-background: 240 5.9% 10%;\\n    --sidebar-foreground: 240 4.8% 95.9%;\\n    --sidebar-primary: 224.3 76.3% 48%;\\n    --sidebar-primary-foreground: 0 0% 100%;\\n    --sidebar-accent: 240 3.7% 15.9%;\\n    --sidebar-accent-foreground: 240 4.8% 95.9%;\\n    --sidebar-border: 240 3.7% 15.9%;\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\n  }\\n}\\n@property --tw-translate-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-translate-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-translate-z {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-rotate-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-rotate-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-rotate-z {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-skew-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-skew-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-space-x-reverse {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-border-style {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: solid;\\n}\\n@property --tw-leading {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-font-weight {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-tracking {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ordinal {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-slashed-zero {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-numeric-figure {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-numeric-spacing {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-numeric-fraction {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-inset-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-inset-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-inset-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-ring-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ring-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-inset-ring-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-inset-ring-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-ring-inset {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ring-offset-width {\\n  syntax: \\\"<length>\\\";\\n  inherits: false;\\n  initial-value: 0px;\\n}\\n@property --tw-ring-offset-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: #fff;\\n}\\n@property --tw-ring-offset-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-outline-style {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: solid;\\n}\\n@property --tw-duration {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ease {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-content {\\n  syntax: \\\"*\\\";\\n  initial-value: \\\"\\\";\\n  inherits: false;\\n}\\n@keyframes pulse {\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n@keyframes enter {\\n  from {\\n    opacity: var(--tw-enter-opacity,1);\\n    transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0));\\n  }\\n}\\n@keyframes exit {\\n  to {\\n    opacity: var(--tw-exit-opacity,1);\\n    transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0));\\n  }\\n}\\n@keyframes accordion-down {\\n  from {\\n    height: 0;\\n  }\\n  to {\\n    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))));\\n  }\\n}\\n@keyframes accordion-up {\\n  from {\\n    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))));\\n  }\\n  to {\\n    height: 0;\\n  }\\n}\\n@layer properties {\\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\\n    *, ::before, ::after, ::backdrop {\\n      --tw-translate-x: 0;\\n      --tw-translate-y: 0;\\n      --tw-translate-z: 0;\\n      --tw-rotate-x: initial;\\n      --tw-rotate-y: initial;\\n      --tw-rotate-z: initial;\\n      --tw-skew-x: initial;\\n      --tw-skew-y: initial;\\n      --tw-space-x-reverse: 0;\\n      --tw-border-style: solid;\\n      --tw-leading: initial;\\n      --tw-font-weight: initial;\\n      --tw-tracking: initial;\\n      --tw-ordinal: initial;\\n      --tw-slashed-zero: initial;\\n      --tw-numeric-figure: initial;\\n      --tw-numeric-spacing: initial;\\n      --tw-numeric-fraction: initial;\\n      --tw-shadow: 0 0 #0000;\\n      --tw-shadow-color: initial;\\n      --tw-shadow-alpha: 100%;\\n      --tw-inset-shadow: 0 0 #0000;\\n      --tw-inset-shadow-color: initial;\\n      --tw-inset-shadow-alpha: 100%;\\n      --tw-ring-color: initial;\\n      --tw-ring-shadow: 0 0 #0000;\\n      --tw-inset-ring-color: initial;\\n      --tw-inset-ring-shadow: 0 0 #0000;\\n      --tw-ring-inset: initial;\\n      --tw-ring-offset-width: 0px;\\n      --tw-ring-offset-color: #fff;\\n      --tw-ring-offset-shadow: 0 0 #0000;\\n      --tw-outline-style: solid;\\n      --tw-duration: initial;\\n      --tw-ease: initial;\\n      --tw-content: \\\"\\\";\\n      --tw-animation-delay: 0s;\\n      --tw-animation-direction: normal;\\n      --tw-animation-duration: initial;\\n      --tw-animation-fill-mode: none;\\n      --tw-animation-iteration-count: 1;\\n      --tw-enter-opacity: 1;\\n      --tw-enter-rotate: 0;\\n      --tw-enter-scale: 1;\\n      --tw-enter-translate-x: 0;\\n      --tw-enter-translate-y: 0;\\n      --tw-exit-opacity: 1;\\n      --tw-exit-rotate: 0;\\n      --tw-exit-scale: 1;\\n      --tw-exit-translate-x: 0;\\n      --tw-exit-translate-y: 0;\\n    }\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"<no source>\",\"webpack://node_modules/tailwindcss/index.css\",\"webpack://node_modules/tw-animate-css/dist/tw-animate.css\",\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,kEAAA;ACs3BE,iBAAmB;AAt3BrB,yCAAyC;AAEzC;EACE;IA0EE,6CAA6C;IAE7C,6CAA6C;IAsE7C,8CAA8C;IAqE9C,6CAA6C;IAC7C,6CAA6C;IAC7C,4CAA4C;IAG5C,6CAA6C;IAC7C,6CAA6C;IAO7C,2CAA2C;IAC3C,4CAA4C;IAG5C,4CAA4C;IAE5C,0CAA0C;IAuC1C,mBAAmB;IACnB,mBAAmB;IAEnB,kBAAkB;IAWlB,qBAAqB;IACrB,qBAAqB;IAUrB,kBAAkB;IAClB,sCAAsC;IACtC,mBAAmB;IACnB,0CAA0C;IAC1C,iBAAiB;IACjB,uCAAuC;IACvC,mBAAmB;IACnB,0CAA0C;IAG1C,kBAAkB;IAClB,sCAAsC;IAmBtC,yBAAyB;IACzB,yBAAyB;IACzB,2BAA2B;IAC3B,uBAAuB;IACvB,4BAA4B;IAI5B,0BAA0B;IAI1B,wBAAwB;IAExB,qBAAqB;IAMrB,qBAAqB;IA6CrB,2CAA2C;IAI3C,+DAA+D;IAoD/D,oCAAoC;IACpC,kEAAkE;IAClE,6CAAoD;IASpD,kDAAyD;EA5c5C;AADJ;AAmeb;EAOE;IAKE,sBAAsB;IACtB,SAAS;IACT,UAAU;IACV,eAAe;EAJM;EAiBvB;IAEE,gBAAgB;IAChB,8BAA8B;IAC9B,WAAW;IACX,2JASC;IACD,mEAGC;IACD,uEAGC;IACD,wCAAwC;EAtBpC;EA+BN;IACE,SAAS;IACT,cAAc;IACd,qBAAqB;EAHpB;EAUH;IACE,yCAAyC;IACzC,iCAAiC;EAFf;EASpB;IAME,kBAAkB;IAClB,oBAAoB;EAFnB;EASH;IACE,cAAc;IACd,gCAAgC;IAChC,wBAAwB;EAHxB;EAUF;IAEE,mBAAmB;EADd;EAWP;IAIE,gJAUC;IACD,wEAGC;IACD,4EAGC;IACD,cAAc;EApBZ;EA2BJ;IACE,cAAc;EADV;EAQN;IAEE,cAAc;IACd,cAAc;IACd,kBAAkB;IAClB,wBAAwB;EAJtB;EAOJ;IACE,eAAe;EADb;EAIJ;IACE,WAAW;EADT;EAUJ;IACE,cAAc;IACd,qBAAqB;IACrB,yBAAyB;EAHrB;EAUN;IACE,aAAa;EADC;EAQhB;IACE,wBAAwB;EADjB;EAQT;IACE,kBAAkB;EADZ;EAQR;IAGE,gBAAgB;EADb;EAUL;IAQE,cAAc;IACd,sBAAsB;EAFjB;EASP;IAEE,eAAe;IACf,YAAY;EAFR;EAYN;IAME,aAAa;IACb,8BAA8B;IAC9B,gCAAgC;IAChC,uBAAuB;IACvB,cAAc;IACd,gBAAgB;IAChB,6BAA6B;IAC7B,UAAU;EARW;EAevB;IACE,mBAAmB;EAD0B;EAQ/C;IACE,0BAA0B;EAD0B;EAQtD;IACE,sBAAsB;EADD;EAQvB;IACE,UAAU;EADE;EASd;IAEE;MACE,mBAAyD;MAAzD;QAAA,yDAAyD;MAAA;IAD7C;EADiC;EAUjD;IACE,gBAAgB;EADT;EAQT;IACE,wBAAwB;EADE;EAS5B;IACE,eAAe;IACf,mBAAmB;EAFS;EAS9B;IACE,oBAAoB;EADE;EAQxB;IACE,UAAU;EAD2B;EAIvC;IASE,gBAAgB;EADqB;EAQvC;IACE,gBAAgB;EADD;EAQjB;IAGE,kBAAkB;EADG;EAQvB;IAEE,YAAY;EADc;EAQ5B;IACE,wBAAwB;EADmB;AA3YnC;AAgZZ;EACE;IAAA,2BAAmB;IAAnB,2BAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,kBAAmB;IAAnB,UAAmB;IAAnB,WAAmB;IAAnB,UAAmB;IAAnB,YAAmB;IAAnB,gBAAmB;IAAnB,sBAAmB;IAAnB,mBAAmB;IAAnB,eAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,qBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,kCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,iBAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,iBAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,qBAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,iCAAmB;IAAnB,kCAAmB;EAAA;EAAnB;IAAA,iCAAmB;IAAnB,kCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,WAAmB;IAAnB,YAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,wBAAmB;EAAA;EAAnB;IAAA,0CAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,+DAAmB;EAAA;EAAnB;IAAA,wDAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,2BAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,0CAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,4CAAmB;EAAA;EAAnB;IAAA,OAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,qEAAmB;EAAA;EAAnB;IAAA,kEAAmB;EAAA;EAAnB;IAAA,+DAAmB;EAAA;EAAnB;IAAA,+DAAmB;EAAA;EAAnB;IAAA,8DAAmB;EAAA;EAAnB;IAAA,+DAAmB;EAAA;EAAnB;IAAA,6CAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,sBAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,qBAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,6CAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,4CAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,kCAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,0GAAmB;EAAA;EAAnB;IAAA,+NAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,6CAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,2BAAmB;EAAA;EAAnB;IAAA,8CAAmB;EAAA;EAAnB;IAAA,0CAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA;MAAA,uBAAmB;MAAnB,+EAAmB;MAAnB,uFAAmB;IAAA;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,gBAAmB;IAAnB,uBAAmB;IAAnB,mBAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,4CAAmB;IAAnB,+CAAmB;EAAA;EAAnB;IAAA,iDAAmB;EAAA;EAAnB;IAAA,kDAAmB;EAAA;EAAnB;IAAA,oCAAmB;IAAnB,iBAAmB;EAAA;EAAnB;IAAA,wCAAmB;IAAnB,qBAAmB;EAAA;EAAnB;IAAA,0CAAmB;IAAnB,uBAAmB;EAAA;EAAnB;IAAA,2CAAmB;IAAnB,wBAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,sBAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,0BAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,2DAAmB;IAAnB;MAAA,0EAAmB;IAAA;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,kCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,8BAAmB;IAAnB;MAAA,oEAAmB;IAAA;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;IAAnB;MAAA,sEAAmB;IAAA;EAAA;EAAnB;IAAA,kCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,0CAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,4DAAmB;EAAA;EAAnB;IAAA,2BAAmB;IAAnB,6DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,eAAmB;IAAnB,cAAmB;EAAA;EAAnB;IAAA,kCAAmB;IAAnB,iCAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,oCAAmB;EAAA;EAAnB;IAAA,8CAAmB;IAAnB,yCAAmB;EAAA;EAAnB;IAAA,2CAAmB;IAAnB,sCAAmB;EAAA;EAAnB;IAAA,2CAAmB;IAAnB,sCAAmB;EAAA;EAAnB;IAAA,6CAAmB;IAAnB,wCAAmB;EAAA;EAAnB;IAAA,oCAAmB;IAAnB,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;IAAnB,sCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,qBAAmB;EAAA;EAAnB;IAAA,wBAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,qBAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,kCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;IAAnB;MAAA,sEAAmB;IAAA;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,kCAAmB;IAAnB,iJAAmB;EAAA;EAAnB;IAAA,0BAAmB;EAAA;EAAnB;IAAA,mCAAmB;IAAnB,kCAAmB;EAAA;EAAnB;IAAA;MAAA,4BAAmB;IAAA;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,yEAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,+HAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,6HAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,sBAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,0HAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,kEAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,wBAAmB;IAAnB,mBAAmB;IAAnB;MAAA,8BAAmB;MAAnB,mBAAmB;IAAA;EAAA;EAAnB;IAAA,sCAAmB;IAAnB,kBAAmB;EAAA;EAAnB;IAAA,qVAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,qCAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,qCAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,mCAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,wBAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,uKAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,4BAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,wDAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,oBAAmB;IAAnB,0BAAmB;EAAA;EAAnB;IAAA,oBAAmB;IAAnB,0BAAmB;EAAA;EAAnB;IAAA,6BAAmB;IAAnB,8CAAmB;EAAA;EAAnB;IAAA,iBAAmB;IAAnB,kCAAmB;EAAA;EAAnB;ICt3B6yL,+BAA6C;IAAE,qBAA+C;EDs3Bx3L;EAAnB;IAAA,wBAAmB;IAAnB,mBAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,iBAAmB;EAAA;EAAnB;ICt3BgmM,6BAA0C;IAA0C,qBAA6C;EDs3B9sM;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,aAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,2CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mEAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yEAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,4CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,4CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,uBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yCAAmB;MAAnB,sBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,4BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;MAAnB,iBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0HAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,uCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,6BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;IAAnB;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;IAAnB;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;MAAnB,iBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,6BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;MAAnB,2DAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,6BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,kBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,sBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,UAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0BAAmB;QAAnB,UAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,wBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,oCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,+BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,oCAAmB;QAAnB;UAAA,0EAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,wCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,8BAAmB;QAAnB;UAAA,oEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,gCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,gCAAmB;QAAnB;UAAA,sEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,kCAAmB;QAAnB;UAAA,wEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,uCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,2DAAmB;QAAnB;UAAA,0EAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,+BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,wBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,uCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,yBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,+BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,aAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0HAAmB;QAAnB,sIAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,yEAAmB;QAAnB,sIAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,gCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,0BAAmB;UAAnB,uCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,4BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0HAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sBAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wHAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,4BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2BAAmB;MAAnB,4GAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wBAAmB;MAAnB,mBAAmB;MAAnB;QAAA,8BAAmB;QAAnB,mBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,wBAAmB;MAAnB,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wHAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wHAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mCAAmB;MAAnB;QAAA,yEAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,4BAAmB;MAAnB;QAAA,kEAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,sCAAmB;MAAnB,kBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2DAAmB;MAAnB;QAAA,0EAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sBAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sBAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mCAAmB;MAAnB;QAAA,yEAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,UAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3BklP,iDAAgE;IDs3B/nP;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3BoqR,8CAA6D;IDs3B9sR;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3B+yQ,iDAAgE;IDs3B51Q;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3By8P,8CAA6D;IDs3Bn/P;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,gBAAmB;QAAnB,oBAAmB;QAAnB,4BAAmB;QAAnB,qBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,aAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,mBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,6BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,8CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0HAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mFAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8NAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;MAAnB,0BAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3By8L,8BAA4C;MAAE,oBAA8C;IDs3BlhM;EAAA;EAAnB;IAAA;MCt3Bw7M,4BAAyC;MAAyC,oBAA4C;IDs3BniN;EAAA;EAAnB;IAAA;MCt3Bu2V,2BAA2B;IDs3B/2V;EAAA;EAAnB;IAAA;MCt3BusW,4BAA4B;IDs3BhtW;EAAA;EAAnB;IAAA;MCt3BqjX,2BAA2B;IDs3B7jX;EAAA;EAAnB;IAAA;MCt3By/U,4BAA4B;IDs3BlgV;EAAA;EAAnB;IAAA;MAAA,qFAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+NAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,kCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;MAAnB,0BAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3B6yL,+BAA6C;MAAE,qBAA+C;IDs3Bx3L;EAAA;EAAnB;IAAA;MCt3BgmM,6BAA0C;MAA0C,qBAA6C;IDs3B9sM;EAAA;EAAnB;IAAA;MCt3B24P,4BAA4B;IDs3Bp5P;EAAA;EAAnB;IAAA;MCt3BkvQ,6BAA6B;IDs3B5vQ;EAAA;EAAnB;IAAA;MCt3BumR,4BAA4B;IDs3BhnR;EAAA;EAAnB;IAAA;MCt3BshP,6BAA6B;IDs3BhiP;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,uCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,uCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,oCAAmB;QAAnB;UAAA,0EAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,yBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;MAAnB,2DAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;MAAnB,2DAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,gCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,qCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,wCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0HAAmB;QAAnB,sIAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,qCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0BAAmB;QAAnB,aAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;MAAnB;QAAA,0EAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;MAAnB;QAAA,oEAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,+BAAmB;UAAnB;YAAA,qEAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,8BAAmB;UAAnB;YAAA,oEAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,mCAAmB;QAAnB;UAAA,yEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,mCAAmB;QAAnB;UAAA,yEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,8BAAmB;QAAnB;UAAA,oEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,wBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,oCAAmB;UAAnB;YAAA,0EAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;MAAnB,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,wBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;MAAnB,iBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,aAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,mBAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,6BAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,oCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,qBAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;MAAnB,uBAAmB;MAAnB,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;MAAnB,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,iCAAmB;MAAnB,kCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;MAAnB,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,2CAAmB;QAAnB,wBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,+BAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,oCAAmB;UAAnB;YAAA,0EAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,gCAAmB;UAAnB;YAAA,sEAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,kCAAmB;UAAnB;YAAA,wEAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,+BAAmB;QAAA;MAAA;IAAA;EAAA;AADJ;ACr3BjB;EAA+B,WAAU;EAAC,eAAc;EAAC,iBAAgB;AAA3C;AAA4C;EAAmC,WAAU;EAAC,eAAc;EAAC,qBAAoB;AAA/C;AAAgD;EAAkC,WAAU;EAAC,eAAc;AAA1B;AAA2B;EAAmC,WAAU;EAAC,eAAc;EAAC,mBAAkB;AAA7C;AAA8C;EAAyC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA6B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA4B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA2B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAAiC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAAiC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA4B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA2B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA0B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAAgC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAAgC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;ACK3hC;EACE,qBAAsB;EACtB,qBAAsB;EACtB,oBAAqB;EACrB,mCAAoC;EACpC,uBAAwB;EACxB,sCAAuC;EAGvC,sCAAuC;EACvC,sCAAuC;EAEvC,wCAAyC;EAEzC,wCAAyC;EACzC,wBAAyB;EACzB,oCAAqC;EACrC,yBAA0B;EAC1B,qCAAsC;EACtC,wCAAyC;EACzC,mDAAoD;EACpD,0BAA2B;EAC3B,yBAA0B;EAC1B,wBAAyB;EACzB,oCAAqC;EACrC,mCAAoC;EACpC,oCAAqC;EACrC,oCAAqC;EACrC,mCAAoC;EACpC,kBAAmB;EACnB,2BAA4B;EAC5B,sCAAuC;EACvC,mCAAoC;EACpC,8CAA+C;EAC/C,iCAAkC;EAClC,6CAA8C;EAC9C,kCAAmC;EACnC,gCAAiC;EAEjC,mCAAoC;EACpC,oCAAqC;EACrC,wCAAyC;EACzC,sCAAuC;EACvC,sCAAuC;EACvC,wCAAyC;EACzC,wCAAyC;EACzC,qCAAsC;EACtC,oCAAqC;EACrC,mCAAoC;EACpC,qCAAsC;EACtC,yCAA0C;AAC3C;AAgED;EACE,6BAA8B;EAC9B,wBAAyB;EACzB,yCAA0C;AAC3C;AAED;EACE,8BAA+B;EAC/B,8BAA+B;EAC/B,wBAAyB;EACzB,mCAAoC;EACpC,2BAA4B;EAC5B,sCAAuC;EACvC,2BAA4B;EAC5B,sCAAuC;EACvC,6BAA8B;EAC9B,wCAAyC;EACzC,yBAA0B;EAC1B,oCAAqC;EACrC,0BAA2B;EAC3B,qCAAsC;EACtC,wCAAyC;EACzC,mDAAoD;EACpD,0BAA2B;EAC3B,yBAA0B;EAC1B,wBAAyB;EACzB,qCAAsC;EACtC,mCAAoC;EACpC,mCAAoC;EACpC,mCAAoC;EACpC,oCAAqC;EACrC,2BAA4B;EAC5B,sCAAuC;EACvC,6CAA8C;EAC9C,8CAA+C;EAC/C,kCAAmC;EACnC,6CAA8C;EAC9C,kCAAmC;EACnC,gCAAiC;AAClC;AAED;EACE;IACS,2BAAa;IAAC,0BAAe;IAAf;MAAA,gEAAe;IAAA;EACrC;EACD;IACS,mCAAa;IAAC,wBAAe;EACrC;AACF;AAED;EACE;IACE,8BAA+B;IAC/B,oCAAqC;IACrC,+BAAgC;IAChC,sCAAuC;IACvC,gCAAiC;IACjC,yCAA0C;IAC1C,6BAA8B;IAC9B,iCAAkC;EACnC;EAED;IACE,kCAAmC;IACnC,oCAAqC;IACrC,kCAAmC;IACnC,uCAAwC;IACxC,gCAAiC;IACjC,2CAA4C;IAC5C,gCAAiC;IACjC,iCAAkC;EACnC;AACF;AFsrBC;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,oBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,kBAAmB;EAAnB,eAAmB;EAAnB,kBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,oBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,iBAAmB;EAAnB,eAAmB;AAAA;AArdjB;EACE;IACE,YAAY;EADV;AADW;ACja0oF;EAAmB;IAAO,kCAAkC;IAAE,qMAAqM;EAA3O;AAAP;AAAsP;EAAkB;IAAK,iCAAiC;IAAE,+LAA+L;EAApO;AAAL;AAAqoB;EAA4B;IAAO,SAAS;EAAX;EAAc;IAAK,+JAA+J;EAAjK;AAAxB;AAA6L;EAA0B;IAAO,+JAA+J;EAAjK;EAAoK;IAAK,SAAS;EAAX;AAA9K;ADs3BxyH;EAAA;IAAA;MAAA,mBAAmB;MAAnB,mBAAmB;MAAnB,mBAAmB;MAAnB,sBAAmB;MAAnB,sBAAmB;MAAnB,sBAAmB;MAAnB,oBAAmB;MAAnB,oBAAmB;MAAnB,uBAAmB;MAAnB,wBAAmB;MAAnB,qBAAmB;MAAnB,yBAAmB;MAAnB,sBAAmB;MAAnB,qBAAmB;MAAnB,0BAAmB;MAAnB,4BAAmB;MAAnB,6BAAmB;MAAnB,8BAAmB;MAAnB,sBAAmB;MAAnB,0BAAmB;MAAnB,uBAAmB;MAAnB,4BAAmB;MAAnB,gCAAmB;MAAnB,6BAAmB;MAAnB,wBAAmB;MAAnB,2BAAmB;MAAnB,8BAAmB;MAAnB,iCAAmB;MAAnB,wBAAmB;MAAnB,2BAAmB;MAAnB,4BAAmB;MAAnB,kCAAmB;MAAnB,yBAAmB;MAAnB,sBAAmB;MAAnB,kBAAmB;MAAnB,gBAAmB;MCt3BrB,wBAA8B;MAA4C,gCAAkC;MAAgD,gCAAiC;MAA2B,8BAAkC;MAA8C,iCAAwC;MAA2C,qBAA4B;MAA2C,oBAA2B;MAA2C,mBAA0B;MAA2C,yBAAgC;MAA2C,yBAAgC;MAA2C,oBAA2B;MAA2C,mBAA0B;MAA2C,kBAAyB;MAA2C,wBAA+B;MAA2C,wBAA+B;IDs3BtgC;EAAA;AAAA\",\"sourcesContent\":[null,\"@layer theme, base, components, utilities;\\n\\n@layer theme {\\n  @theme default {\\n    --font-sans:\\n      ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n    --font-serif: ui-serif, Georgia, Cambria, \\\"Times New Roman\\\", Times, serif;\\n    --font-mono:\\n      ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\", monospace;\\n\\n    --color-red-50: oklch(97.1% 0.013 17.38);\\n    --color-red-100: oklch(93.6% 0.032 17.717);\\n    --color-red-200: oklch(88.5% 0.062 18.334);\\n    --color-red-300: oklch(80.8% 0.114 19.571);\\n    --color-red-400: oklch(70.4% 0.191 22.216);\\n    --color-red-500: oklch(63.7% 0.237 25.331);\\n    --color-red-600: oklch(57.7% 0.245 27.325);\\n    --color-red-700: oklch(50.5% 0.213 27.518);\\n    --color-red-800: oklch(44.4% 0.177 26.899);\\n    --color-red-900: oklch(39.6% 0.141 25.723);\\n    --color-red-950: oklch(25.8% 0.092 26.042);\\n\\n    --color-orange-50: oklch(98% 0.016 73.684);\\n    --color-orange-100: oklch(95.4% 0.038 75.164);\\n    --color-orange-200: oklch(90.1% 0.076 70.697);\\n    --color-orange-300: oklch(83.7% 0.128 66.29);\\n    --color-orange-400: oklch(75% 0.183 55.934);\\n    --color-orange-500: oklch(70.5% 0.213 47.604);\\n    --color-orange-600: oklch(64.6% 0.222 41.116);\\n    --color-orange-700: oklch(55.3% 0.195 38.402);\\n    --color-orange-800: oklch(47% 0.157 37.304);\\n    --color-orange-900: oklch(40.8% 0.123 38.172);\\n    --color-orange-950: oklch(26.6% 0.079 36.259);\\n\\n    --color-amber-50: oklch(98.7% 0.022 95.277);\\n    --color-amber-100: oklch(96.2% 0.059 95.617);\\n    --color-amber-200: oklch(92.4% 0.12 95.746);\\n    --color-amber-300: oklch(87.9% 0.169 91.605);\\n    --color-amber-400: oklch(82.8% 0.189 84.429);\\n    --color-amber-500: oklch(76.9% 0.188 70.08);\\n    --color-amber-600: oklch(66.6% 0.179 58.318);\\n    --color-amber-700: oklch(55.5% 0.163 48.998);\\n    --color-amber-800: oklch(47.3% 0.137 46.201);\\n    --color-amber-900: oklch(41.4% 0.112 45.904);\\n    --color-amber-950: oklch(27.9% 0.077 45.635);\\n\\n    --color-yellow-50: oklch(98.7% 0.026 102.212);\\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\\n    --color-yellow-300: oklch(90.5% 0.182 98.111);\\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\\n    --color-yellow-600: oklch(68.1% 0.162 75.834);\\n    --color-yellow-700: oklch(55.4% 0.135 66.442);\\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\\n    --color-yellow-900: oklch(42.1% 0.095 57.708);\\n    --color-yellow-950: oklch(28.6% 0.066 53.813);\\n\\n    --color-lime-50: oklch(98.6% 0.031 120.757);\\n    --color-lime-100: oklch(96.7% 0.067 122.328);\\n    --color-lime-200: oklch(93.8% 0.127 124.321);\\n    --color-lime-300: oklch(89.7% 0.196 126.665);\\n    --color-lime-400: oklch(84.1% 0.238 128.85);\\n    --color-lime-500: oklch(76.8% 0.233 130.85);\\n    --color-lime-600: oklch(64.8% 0.2 131.684);\\n    --color-lime-700: oklch(53.2% 0.157 131.589);\\n    --color-lime-800: oklch(45.3% 0.124 130.933);\\n    --color-lime-900: oklch(40.5% 0.101 131.063);\\n    --color-lime-950: oklch(27.4% 0.072 132.109);\\n\\n    --color-green-50: oklch(98.2% 0.018 155.826);\\n    --color-green-100: oklch(96.2% 0.044 156.743);\\n    --color-green-200: oklch(92.5% 0.084 155.995);\\n    --color-green-300: oklch(87.1% 0.15 154.449);\\n    --color-green-400: oklch(79.2% 0.209 151.711);\\n    --color-green-500: oklch(72.3% 0.219 149.579);\\n    --color-green-600: oklch(62.7% 0.194 149.214);\\n    --color-green-700: oklch(52.7% 0.154 150.069);\\n    --color-green-800: oklch(44.8% 0.119 151.328);\\n    --color-green-900: oklch(39.3% 0.095 152.535);\\n    --color-green-950: oklch(26.6% 0.065 152.934);\\n\\n    --color-emerald-50: oklch(97.9% 0.021 166.113);\\n    --color-emerald-100: oklch(95% 0.052 163.051);\\n    --color-emerald-200: oklch(90.5% 0.093 164.15);\\n    --color-emerald-300: oklch(84.5% 0.143 164.978);\\n    --color-emerald-400: oklch(76.5% 0.177 163.223);\\n    --color-emerald-500: oklch(69.6% 0.17 162.48);\\n    --color-emerald-600: oklch(59.6% 0.145 163.225);\\n    --color-emerald-700: oklch(50.8% 0.118 165.612);\\n    --color-emerald-800: oklch(43.2% 0.095 166.913);\\n    --color-emerald-900: oklch(37.8% 0.077 168.94);\\n    --color-emerald-950: oklch(26.2% 0.051 172.552);\\n\\n    --color-teal-50: oklch(98.4% 0.014 180.72);\\n    --color-teal-100: oklch(95.3% 0.051 180.801);\\n    --color-teal-200: oklch(91% 0.096 180.426);\\n    --color-teal-300: oklch(85.5% 0.138 181.071);\\n    --color-teal-400: oklch(77.7% 0.152 181.912);\\n    --color-teal-500: oklch(70.4% 0.14 182.503);\\n    --color-teal-600: oklch(60% 0.118 184.704);\\n    --color-teal-700: oklch(51.1% 0.096 186.391);\\n    --color-teal-800: oklch(43.7% 0.078 188.216);\\n    --color-teal-900: oklch(38.6% 0.063 188.416);\\n    --color-teal-950: oklch(27.7% 0.046 192.524);\\n\\n    --color-cyan-50: oklch(98.4% 0.019 200.873);\\n    --color-cyan-100: oklch(95.6% 0.045 203.388);\\n    --color-cyan-200: oklch(91.7% 0.08 205.041);\\n    --color-cyan-300: oklch(86.5% 0.127 207.078);\\n    --color-cyan-400: oklch(78.9% 0.154 211.53);\\n    --color-cyan-500: oklch(71.5% 0.143 215.221);\\n    --color-cyan-600: oklch(60.9% 0.126 221.723);\\n    --color-cyan-700: oklch(52% 0.105 223.128);\\n    --color-cyan-800: oklch(45% 0.085 224.283);\\n    --color-cyan-900: oklch(39.8% 0.07 227.392);\\n    --color-cyan-950: oklch(30.2% 0.056 229.695);\\n\\n    --color-sky-50: oklch(97.7% 0.013 236.62);\\n    --color-sky-100: oklch(95.1% 0.026 236.824);\\n    --color-sky-200: oklch(90.1% 0.058 230.902);\\n    --color-sky-300: oklch(82.8% 0.111 230.318);\\n    --color-sky-400: oklch(74.6% 0.16 232.661);\\n    --color-sky-500: oklch(68.5% 0.169 237.323);\\n    --color-sky-600: oklch(58.8% 0.158 241.966);\\n    --color-sky-700: oklch(50% 0.134 242.749);\\n    --color-sky-800: oklch(44.3% 0.11 240.79);\\n    --color-sky-900: oklch(39.1% 0.09 240.876);\\n    --color-sky-950: oklch(29.3% 0.066 243.157);\\n\\n    --color-blue-50: oklch(97% 0.014 254.604);\\n    --color-blue-100: oklch(93.2% 0.032 255.585);\\n    --color-blue-200: oklch(88.2% 0.059 254.128);\\n    --color-blue-300: oklch(80.9% 0.105 251.813);\\n    --color-blue-400: oklch(70.7% 0.165 254.624);\\n    --color-blue-500: oklch(62.3% 0.214 259.815);\\n    --color-blue-600: oklch(54.6% 0.245 262.881);\\n    --color-blue-700: oklch(48.8% 0.243 264.376);\\n    --color-blue-800: oklch(42.4% 0.199 265.638);\\n    --color-blue-900: oklch(37.9% 0.146 265.522);\\n    --color-blue-950: oklch(28.2% 0.091 267.935);\\n\\n    --color-indigo-50: oklch(96.2% 0.018 272.314);\\n    --color-indigo-100: oklch(93% 0.034 272.788);\\n    --color-indigo-200: oklch(87% 0.065 274.039);\\n    --color-indigo-300: oklch(78.5% 0.115 274.713);\\n    --color-indigo-400: oklch(67.3% 0.182 276.935);\\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\\n    --color-indigo-600: oklch(51.1% 0.262 276.966);\\n    --color-indigo-700: oklch(45.7% 0.24 277.023);\\n    --color-indigo-800: oklch(39.8% 0.195 277.366);\\n    --color-indigo-900: oklch(35.9% 0.144 278.697);\\n    --color-indigo-950: oklch(25.7% 0.09 281.288);\\n\\n    --color-violet-50: oklch(96.9% 0.016 293.756);\\n    --color-violet-100: oklch(94.3% 0.029 294.588);\\n    --color-violet-200: oklch(89.4% 0.057 293.283);\\n    --color-violet-300: oklch(81.1% 0.111 293.571);\\n    --color-violet-400: oklch(70.2% 0.183 293.541);\\n    --color-violet-500: oklch(60.6% 0.25 292.717);\\n    --color-violet-600: oklch(54.1% 0.281 293.009);\\n    --color-violet-700: oklch(49.1% 0.27 292.581);\\n    --color-violet-800: oklch(43.2% 0.232 292.759);\\n    --color-violet-900: oklch(38% 0.189 293.745);\\n    --color-violet-950: oklch(28.3% 0.141 291.089);\\n\\n    --color-purple-50: oklch(97.7% 0.014 308.299);\\n    --color-purple-100: oklch(94.6% 0.033 307.174);\\n    --color-purple-200: oklch(90.2% 0.063 306.703);\\n    --color-purple-300: oklch(82.7% 0.119 306.383);\\n    --color-purple-400: oklch(71.4% 0.203 305.504);\\n    --color-purple-500: oklch(62.7% 0.265 303.9);\\n    --color-purple-600: oklch(55.8% 0.288 302.321);\\n    --color-purple-700: oklch(49.6% 0.265 301.924);\\n    --color-purple-800: oklch(43.8% 0.218 303.724);\\n    --color-purple-900: oklch(38.1% 0.176 304.987);\\n    --color-purple-950: oklch(29.1% 0.149 302.717);\\n\\n    --color-fuchsia-50: oklch(97.7% 0.017 320.058);\\n    --color-fuchsia-100: oklch(95.2% 0.037 318.852);\\n    --color-fuchsia-200: oklch(90.3% 0.076 319.62);\\n    --color-fuchsia-300: oklch(83.3% 0.145 321.434);\\n    --color-fuchsia-400: oklch(74% 0.238 322.16);\\n    --color-fuchsia-500: oklch(66.7% 0.295 322.15);\\n    --color-fuchsia-600: oklch(59.1% 0.293 322.896);\\n    --color-fuchsia-700: oklch(51.8% 0.253 323.949);\\n    --color-fuchsia-800: oklch(45.2% 0.211 324.591);\\n    --color-fuchsia-900: oklch(40.1% 0.17 325.612);\\n    --color-fuchsia-950: oklch(29.3% 0.136 325.661);\\n\\n    --color-pink-50: oklch(97.1% 0.014 343.198);\\n    --color-pink-100: oklch(94.8% 0.028 342.258);\\n    --color-pink-200: oklch(89.9% 0.061 343.231);\\n    --color-pink-300: oklch(82.3% 0.12 346.018);\\n    --color-pink-400: oklch(71.8% 0.202 349.761);\\n    --color-pink-500: oklch(65.6% 0.241 354.308);\\n    --color-pink-600: oklch(59.2% 0.249 0.584);\\n    --color-pink-700: oklch(52.5% 0.223 3.958);\\n    --color-pink-800: oklch(45.9% 0.187 3.815);\\n    --color-pink-900: oklch(40.8% 0.153 2.432);\\n    --color-pink-950: oklch(28.4% 0.109 3.907);\\n\\n    --color-rose-50: oklch(96.9% 0.015 12.422);\\n    --color-rose-100: oklch(94.1% 0.03 12.58);\\n    --color-rose-200: oklch(89.2% 0.058 10.001);\\n    --color-rose-300: oklch(81% 0.117 11.638);\\n    --color-rose-400: oklch(71.2% 0.194 13.428);\\n    --color-rose-500: oklch(64.5% 0.246 16.439);\\n    --color-rose-600: oklch(58.6% 0.253 17.585);\\n    --color-rose-700: oklch(51.4% 0.222 16.935);\\n    --color-rose-800: oklch(45.5% 0.188 13.697);\\n    --color-rose-900: oklch(41% 0.159 10.272);\\n    --color-rose-950: oklch(27.1% 0.105 12.094);\\n\\n    --color-slate-50: oklch(98.4% 0.003 247.858);\\n    --color-slate-100: oklch(96.8% 0.007 247.896);\\n    --color-slate-200: oklch(92.9% 0.013 255.508);\\n    --color-slate-300: oklch(86.9% 0.022 252.894);\\n    --color-slate-400: oklch(70.4% 0.04 256.788);\\n    --color-slate-500: oklch(55.4% 0.046 257.417);\\n    --color-slate-600: oklch(44.6% 0.043 257.281);\\n    --color-slate-700: oklch(37.2% 0.044 257.287);\\n    --color-slate-800: oklch(27.9% 0.041 260.031);\\n    --color-slate-900: oklch(20.8% 0.042 265.755);\\n    --color-slate-950: oklch(12.9% 0.042 264.695);\\n\\n    --color-gray-50: oklch(98.5% 0.002 247.839);\\n    --color-gray-100: oklch(96.7% 0.003 264.542);\\n    --color-gray-200: oklch(92.8% 0.006 264.531);\\n    --color-gray-300: oklch(87.2% 0.01 258.338);\\n    --color-gray-400: oklch(70.7% 0.022 261.325);\\n    --color-gray-500: oklch(55.1% 0.027 264.364);\\n    --color-gray-600: oklch(44.6% 0.03 256.802);\\n    --color-gray-700: oklch(37.3% 0.034 259.733);\\n    --color-gray-800: oklch(27.8% 0.033 256.848);\\n    --color-gray-900: oklch(21% 0.034 264.665);\\n    --color-gray-950: oklch(13% 0.028 261.692);\\n\\n    --color-zinc-50: oklch(98.5% 0 0);\\n    --color-zinc-100: oklch(96.7% 0.001 286.375);\\n    --color-zinc-200: oklch(92% 0.004 286.32);\\n    --color-zinc-300: oklch(87.1% 0.006 286.286);\\n    --color-zinc-400: oklch(70.5% 0.015 286.067);\\n    --color-zinc-500: oklch(55.2% 0.016 285.938);\\n    --color-zinc-600: oklch(44.2% 0.017 285.786);\\n    --color-zinc-700: oklch(37% 0.013 285.805);\\n    --color-zinc-800: oklch(27.4% 0.006 286.033);\\n    --color-zinc-900: oklch(21% 0.006 285.885);\\n    --color-zinc-950: oklch(14.1% 0.005 285.823);\\n\\n    --color-neutral-50: oklch(98.5% 0 0);\\n    --color-neutral-100: oklch(97% 0 0);\\n    --color-neutral-200: oklch(92.2% 0 0);\\n    --color-neutral-300: oklch(87% 0 0);\\n    --color-neutral-400: oklch(70.8% 0 0);\\n    --color-neutral-500: oklch(55.6% 0 0);\\n    --color-neutral-600: oklch(43.9% 0 0);\\n    --color-neutral-700: oklch(37.1% 0 0);\\n    --color-neutral-800: oklch(26.9% 0 0);\\n    --color-neutral-900: oklch(20.5% 0 0);\\n    --color-neutral-950: oklch(14.5% 0 0);\\n\\n    --color-stone-50: oklch(98.5% 0.001 106.423);\\n    --color-stone-100: oklch(97% 0.001 106.424);\\n    --color-stone-200: oklch(92.3% 0.003 48.717);\\n    --color-stone-300: oklch(86.9% 0.005 56.366);\\n    --color-stone-400: oklch(70.9% 0.01 56.259);\\n    --color-stone-500: oklch(55.3% 0.013 58.071);\\n    --color-stone-600: oklch(44.4% 0.011 73.639);\\n    --color-stone-700: oklch(37.4% 0.01 67.558);\\n    --color-stone-800: oklch(26.8% 0.007 34.298);\\n    --color-stone-900: oklch(21.6% 0.006 56.043);\\n    --color-stone-950: oklch(14.7% 0.004 49.25);\\n\\n    --color-black: #000;\\n    --color-white: #fff;\\n\\n    --spacing: 0.25rem;\\n\\n    --breakpoint-sm: 40rem;\\n    --breakpoint-md: 48rem;\\n    --breakpoint-lg: 64rem;\\n    --breakpoint-xl: 80rem;\\n    --breakpoint-2xl: 96rem;\\n\\n    --container-3xs: 16rem;\\n    --container-2xs: 18rem;\\n    --container-xs: 20rem;\\n    --container-sm: 24rem;\\n    --container-md: 28rem;\\n    --container-lg: 32rem;\\n    --container-xl: 36rem;\\n    --container-2xl: 42rem;\\n    --container-3xl: 48rem;\\n    --container-4xl: 56rem;\\n    --container-5xl: 64rem;\\n    --container-6xl: 72rem;\\n    --container-7xl: 80rem;\\n\\n    --text-xs: 0.75rem;\\n    --text-xs--line-height: calc(1 / 0.75);\\n    --text-sm: 0.875rem;\\n    --text-sm--line-height: calc(1.25 / 0.875);\\n    --text-base: 1rem;\\n    --text-base--line-height: calc(1.5 / 1);\\n    --text-lg: 1.125rem;\\n    --text-lg--line-height: calc(1.75 / 1.125);\\n    --text-xl: 1.25rem;\\n    --text-xl--line-height: calc(1.75 / 1.25);\\n    --text-2xl: 1.5rem;\\n    --text-2xl--line-height: calc(2 / 1.5);\\n    --text-3xl: 1.875rem;\\n    --text-3xl--line-height: calc(2.25 / 1.875);\\n    --text-4xl: 2.25rem;\\n    --text-4xl--line-height: calc(2.5 / 2.25);\\n    --text-5xl: 3rem;\\n    --text-5xl--line-height: 1;\\n    --text-6xl: 3.75rem;\\n    --text-6xl--line-height: 1;\\n    --text-7xl: 4.5rem;\\n    --text-7xl--line-height: 1;\\n    --text-8xl: 6rem;\\n    --text-8xl--line-height: 1;\\n    --text-9xl: 8rem;\\n    --text-9xl--line-height: 1;\\n\\n    --font-weight-thin: 100;\\n    --font-weight-extralight: 200;\\n    --font-weight-light: 300;\\n    --font-weight-normal: 400;\\n    --font-weight-medium: 500;\\n    --font-weight-semibold: 600;\\n    --font-weight-bold: 700;\\n    --font-weight-extrabold: 800;\\n    --font-weight-black: 900;\\n\\n    --tracking-tighter: -0.05em;\\n    --tracking-tight: -0.025em;\\n    --tracking-normal: 0em;\\n    --tracking-wide: 0.025em;\\n    --tracking-wider: 0.05em;\\n    --tracking-widest: 0.1em;\\n\\n    --leading-tight: 1.25;\\n    --leading-snug: 1.375;\\n    --leading-normal: 1.5;\\n    --leading-relaxed: 1.625;\\n    --leading-loose: 2;\\n\\n    --radius-xs: 0.125rem;\\n    --radius-sm: 0.25rem;\\n    --radius-md: 0.375rem;\\n    --radius-lg: 0.5rem;\\n    --radius-xl: 0.75rem;\\n    --radius-2xl: 1rem;\\n    --radius-3xl: 1.5rem;\\n    --radius-4xl: 2rem;\\n\\n    --shadow-2xs: 0 1px rgb(0 0 0 / 0.05);\\n    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-md:\\n      0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n    --shadow-lg:\\n      0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl:\\n      0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n\\n    --inset-shadow-2xs: inset 0 1px rgb(0 0 0 / 0.05);\\n    --inset-shadow-xs: inset 0 1px 1px rgb(0 0 0 / 0.05);\\n    --inset-shadow-sm: inset 0 2px 4px rgb(0 0 0 / 0.05);\\n\\n    --drop-shadow-xs: 0 1px 1px rgb(0 0 0 / 0.05);\\n    --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);\\n    --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);\\n    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);\\n    --drop-shadow-xl: 0 9px 7px rgb(0 0 0 / 0.1);\\n    --drop-shadow-2xl: 0 25px 25px rgb(0 0 0 / 0.15);\\n\\n    --text-shadow-2xs: 0px 1px 0px rgb(0 0 0 / 0.15);\\n    --text-shadow-xs: 0px 1px 1px rgb(0 0 0 / 0.2);\\n    --text-shadow-sm:\\n      0px 1px 0px rgb(0 0 0 / 0.075), 0px 1px 1px rgb(0 0 0 / 0.075),\\n      0px 2px 2px rgb(0 0 0 / 0.075);\\n    --text-shadow-md:\\n      0px 1px 1px rgb(0 0 0 / 0.1), 0px 1px 2px rgb(0 0 0 / 0.1),\\n      0px 2px 4px rgb(0 0 0 / 0.1);\\n    --text-shadow-lg:\\n      0px 1px 2px rgb(0 0 0 / 0.1), 0px 3px 2px rgb(0 0 0 / 0.1),\\n      0px 4px 8px rgb(0 0 0 / 0.1);\\n\\n    --ease-in: cubic-bezier(0.4, 0, 1, 1);\\n    --ease-out: cubic-bezier(0, 0, 0.2, 1);\\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\\n\\n    --animate-spin: spin 1s linear infinite;\\n    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n    --animate-bounce: bounce 1s infinite;\\n\\n    @keyframes spin {\\n      to {\\n        transform: rotate(360deg);\\n      }\\n    }\\n\\n    @keyframes ping {\\n      75%,\\n      100% {\\n        transform: scale(2);\\n        opacity: 0;\\n      }\\n    }\\n\\n    @keyframes pulse {\\n      50% {\\n        opacity: 0.5;\\n      }\\n    }\\n\\n    @keyframes bounce {\\n      0%,\\n      100% {\\n        transform: translateY(-25%);\\n        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n      }\\n\\n      50% {\\n        transform: none;\\n        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n      }\\n    }\\n\\n    --blur-xs: 4px;\\n    --blur-sm: 8px;\\n    --blur-md: 12px;\\n    --blur-lg: 16px;\\n    --blur-xl: 24px;\\n    --blur-2xl: 40px;\\n    --blur-3xl: 64px;\\n\\n    --perspective-dramatic: 100px;\\n    --perspective-near: 300px;\\n    --perspective-normal: 500px;\\n    --perspective-midrange: 800px;\\n    --perspective-distant: 1200px;\\n\\n    --aspect-video: 16 / 9;\\n\\n    --default-transition-duration: 150ms;\\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    --default-font-family: --theme(--font-sans, initial);\\n    --default-font-feature-settings: --theme(\\n      --font-sans--font-feature-settings,\\n      initial\\n    );\\n    --default-font-variation-settings: --theme(\\n      --font-sans--font-variation-settings,\\n      initial\\n    );\\n    --default-mono-font-family: --theme(--font-mono, initial);\\n    --default-mono-font-feature-settings: --theme(\\n      --font-mono--font-feature-settings,\\n      initial\\n    );\\n    --default-mono-font-variation-settings: --theme(\\n      --font-mono--font-variation-settings,\\n      initial\\n    );\\n  }\\n\\n  /* Deprecated */\\n  @theme default inline reference {\\n    --blur: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\\n    --drop-shadow: 0 1px 2px rgb(0 0 0 / 0.1), 0 1px 1px rgb(0 0 0 / 0.06);\\n    --radius: 0.25rem;\\n    --max-width-prose: 65ch;\\n  }\\n}\\n\\n@layer base {\\n  /*\\n  1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n  2. Remove default margins and padding\\n  3. Reset all borders.\\n*/\\n\\n  *,\\n  ::after,\\n  ::before,\\n  ::backdrop,\\n  ::file-selector-button {\\n    box-sizing: border-box; /* 1 */\\n    margin: 0; /* 2 */\\n    padding: 0; /* 2 */\\n    border: 0 solid; /* 3 */\\n  }\\n\\n  /*\\n  1. Use a consistent sensible line-height in all browsers.\\n  2. Prevent adjustments of font size after orientation changes in iOS.\\n  3. Use a more readable tab size.\\n  4. Use the user's configured `sans` font-family by default.\\n  5. Use the user's configured `sans` font-feature-settings by default.\\n  6. Use the user's configured `sans` font-variation-settings by default.\\n  7. Disable tap highlights on iOS.\\n*/\\n\\n  html,\\n  :host {\\n    line-height: 1.5; /* 1 */\\n    -webkit-text-size-adjust: 100%; /* 2 */\\n    tab-size: 4; /* 3 */\\n    font-family: --theme(\\n      --default-font-family,\\n      ui-sans-serif,\\n      system-ui,\\n      sans-serif,\\n      \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\",\\n      \\\"Segoe UI Symbol\\\",\\n      \\\"Noto Color Emoji\\\"\\n    ); /* 4 */\\n    font-feature-settings: --theme(\\n      --default-font-feature-settings,\\n      normal\\n    ); /* 5 */\\n    font-variation-settings: --theme(\\n      --default-font-variation-settings,\\n      normal\\n    ); /* 6 */\\n    -webkit-tap-highlight-color: transparent; /* 7 */\\n  }\\n\\n  /*\\n  1. Add the correct height in Firefox.\\n  2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n  3. Reset the default border style to a 1px solid border.\\n*/\\n\\n  hr {\\n    height: 0; /* 1 */\\n    color: inherit; /* 2 */\\n    border-top-width: 1px; /* 3 */\\n  }\\n\\n  /*\\n  Add the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\n  abbr:where([title]) {\\n    -webkit-text-decoration: underline dotted;\\n    text-decoration: underline dotted;\\n  }\\n\\n  /*\\n  Remove the default font size and weight for headings.\\n*/\\n\\n  h1,\\n  h2,\\n  h3,\\n  h4,\\n  h5,\\n  h6 {\\n    font-size: inherit;\\n    font-weight: inherit;\\n  }\\n\\n  /*\\n  Reset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\n  a {\\n    color: inherit;\\n    -webkit-text-decoration: inherit;\\n    text-decoration: inherit;\\n  }\\n\\n  /*\\n  Add the correct font weight in Edge and Safari.\\n*/\\n\\n  b,\\n  strong {\\n    font-weight: bolder;\\n  }\\n\\n  /*\\n  1. Use the user's configured `mono` font-family by default.\\n  2. Use the user's configured `mono` font-feature-settings by default.\\n  3. Use the user's configured `mono` font-variation-settings by default.\\n  4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\n  code,\\n  kbd,\\n  samp,\\n  pre {\\n    font-family: --theme(\\n      --default-mono-font-family,\\n      ui-monospace,\\n      SFMono-Regular,\\n      Menlo,\\n      Monaco,\\n      Consolas,\\n      \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\",\\n      monospace\\n    ); /* 1 */\\n    font-feature-settings: --theme(\\n      --default-mono-font-feature-settings,\\n      normal\\n    ); /* 2 */\\n    font-variation-settings: --theme(\\n      --default-mono-font-variation-settings,\\n      normal\\n    ); /* 3 */\\n    font-size: 1em; /* 4 */\\n  }\\n\\n  /*\\n  Add the correct font size in all browsers.\\n*/\\n\\n  small {\\n    font-size: 80%;\\n  }\\n\\n  /*\\n  Prevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\n  sub,\\n  sup {\\n    font-size: 75%;\\n    line-height: 0;\\n    position: relative;\\n    vertical-align: baseline;\\n  }\\n\\n  sub {\\n    bottom: -0.25em;\\n  }\\n\\n  sup {\\n    top: -0.5em;\\n  }\\n\\n  /*\\n  1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n  2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n  3. Remove gaps between table borders by default.\\n*/\\n\\n  table {\\n    text-indent: 0; /* 1 */\\n    border-color: inherit; /* 2 */\\n    border-collapse: collapse; /* 3 */\\n  }\\n\\n  /*\\n  Use the modern Firefox focus style for all focusable elements.\\n*/\\n\\n  :-moz-focusring {\\n    outline: auto;\\n  }\\n\\n  /*\\n  Add the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\n  progress {\\n    vertical-align: baseline;\\n  }\\n\\n  /*\\n  Add the correct display in Chrome and Safari.\\n*/\\n\\n  summary {\\n    display: list-item;\\n  }\\n\\n  /*\\n  Make lists unstyled by default.\\n*/\\n\\n  ol,\\n  ul,\\n  menu {\\n    list-style: none;\\n  }\\n\\n  /*\\n  1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n  2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n      This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\n  img,\\n  svg,\\n  video,\\n  canvas,\\n  audio,\\n  iframe,\\n  embed,\\n  object {\\n    display: block; /* 1 */\\n    vertical-align: middle; /* 2 */\\n  }\\n\\n  /*\\n  Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\n  img,\\n  video {\\n    max-width: 100%;\\n    height: auto;\\n  }\\n\\n  /*\\n  1. Inherit font styles in all browsers.\\n  2. Remove border radius in all browsers.\\n  3. Remove background color in all browsers.\\n  4. Ensure consistent opacity for disabled states in all browsers.\\n*/\\n\\n  button,\\n  input,\\n  select,\\n  optgroup,\\n  textarea,\\n  ::file-selector-button {\\n    font: inherit; /* 1 */\\n    font-feature-settings: inherit; /* 1 */\\n    font-variation-settings: inherit; /* 1 */\\n    letter-spacing: inherit; /* 1 */\\n    color: inherit; /* 1 */\\n    border-radius: 0; /* 2 */\\n    background-color: transparent; /* 3 */\\n    opacity: 1; /* 4 */\\n  }\\n\\n  /*\\n  Restore default font weight.\\n*/\\n\\n  :where(select:is([multiple], [size])) optgroup {\\n    font-weight: bolder;\\n  }\\n\\n  /*\\n  Restore indentation.\\n*/\\n\\n  :where(select:is([multiple], [size])) optgroup option {\\n    padding-inline-start: 20px;\\n  }\\n\\n  /*\\n  Restore space after button.\\n*/\\n\\n  ::file-selector-button {\\n    margin-inline-end: 4px;\\n  }\\n\\n  /*\\n  Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n*/\\n\\n  ::placeholder {\\n    opacity: 1;\\n  }\\n\\n  /*\\n  Set the default placeholder color to a semi-transparent version of the current text color in browsers that do not\\n  crash when using `color-mix(…)` with `currentcolor`. (https://github.com/tailwindlabs/tailwindcss/issues/17194)\\n*/\\n\\n  @supports (not (-webkit-appearance: -apple-pay-button)) /* Not Safari */ or\\n    (contain-intrinsic-size: 1px) /* Safari 17+ */ {\\n    ::placeholder {\\n      color: color-mix(in oklab, currentcolor 50%, transparent);\\n    }\\n  }\\n\\n  /*\\n  Prevent resizing textareas horizontally by default.\\n*/\\n\\n  textarea {\\n    resize: vertical;\\n  }\\n\\n  /*\\n  Remove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n  ::-webkit-search-decoration {\\n    -webkit-appearance: none;\\n  }\\n\\n  /*\\n  1. Ensure date/time inputs have the same height when empty in iOS Safari.\\n  2. Ensure text alignment can be changed on date/time inputs in iOS Safari.\\n*/\\n\\n  ::-webkit-date-and-time-value {\\n    min-height: 1lh; /* 1 */\\n    text-align: inherit; /* 2 */\\n  }\\n\\n  /*\\n  Prevent height from changing on date/time inputs in macOS Safari when the input is set to `display: block`.\\n*/\\n\\n  ::-webkit-datetime-edit {\\n    display: inline-flex;\\n  }\\n\\n  /*\\n  Remove excess padding from pseudo-elements in date/time inputs to ensure consistent height across browsers.\\n*/\\n\\n  ::-webkit-datetime-edit-fields-wrapper {\\n    padding: 0;\\n  }\\n\\n  ::-webkit-datetime-edit,\\n  ::-webkit-datetime-edit-year-field,\\n  ::-webkit-datetime-edit-month-field,\\n  ::-webkit-datetime-edit-day-field,\\n  ::-webkit-datetime-edit-hour-field,\\n  ::-webkit-datetime-edit-minute-field,\\n  ::-webkit-datetime-edit-second-field,\\n  ::-webkit-datetime-edit-millisecond-field,\\n  ::-webkit-datetime-edit-meridiem-field {\\n    padding-block: 0;\\n  }\\n\\n  /*\\n  Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n  :-moz-ui-invalid {\\n    box-shadow: none;\\n  }\\n\\n  /*\\n  Correct the inability to style the border radius in iOS Safari.\\n*/\\n\\n  button,\\n  input:where([type=\\\"button\\\"], [type=\\\"reset\\\"], [type=\\\"submit\\\"]),\\n  ::file-selector-button {\\n    appearance: button;\\n  }\\n\\n  /*\\n  Correct the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n  ::-webkit-inner-spin-button,\\n  ::-webkit-outer-spin-button {\\n    height: auto;\\n  }\\n\\n  /*\\n  Make elements with the HTML hidden attribute stay hidden by default.\\n*/\\n\\n  [hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n    display: none !important;\\n  }\\n}\\n\\n@layer utilities {\\n  @tailwind utilities;\\n}\\n\",\"@property --tw-animation-delay{syntax:\\\"*\\\";inherits:false;initial-value:0s}@property --tw-animation-direction{syntax:\\\"*\\\";inherits:false;initial-value:normal}@property --tw-animation-duration{syntax:\\\"*\\\";inherits:false}@property --tw-animation-fill-mode{syntax:\\\"*\\\";inherits:false;initial-value:none}@property --tw-animation-iteration-count{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-enter-opacity{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-enter-rotate{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-enter-scale{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-enter-translate-x{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-enter-translate-y{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-exit-opacity{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-exit-rotate{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-exit-scale{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-exit-translate-x{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-exit-translate-y{syntax:\\\"*\\\";inherits:false;initial-value:0}@theme inline{--animation-delay-0: 0s; --animation-delay-75: 75ms; --animation-delay-100: .1s; --animation-delay-150: .15s; --animation-delay-200: .2s; --animation-delay-300: .3s; --animation-delay-500: .5s; --animation-delay-700: .7s; --animation-delay-1000: 1s; --animation-repeat-0: 0; --animation-repeat-1: 1; --animation-repeat-infinite: infinite; --animation-direction-normal: normal; --animation-direction-reverse: reverse; --animation-direction-alternate: alternate; --animation-direction-alternate-reverse: alternate-reverse; --animation-fill-mode-none: none; --animation-fill-mode-forwards: forwards; --animation-fill-mode-backwards: backwards; --animation-fill-mode-both: both; --percentage-0: 0; --percentage-5: .05; --percentage-10: .1; --percentage-15: .15; --percentage-20: .2; --percentage-25: .25; --percentage-30: .3; --percentage-35: .35; --percentage-40: .4; --percentage-45: .45; --percentage-50: .5; --percentage-55: .55; --percentage-60: .6; --percentage-65: .65; --percentage-70: .7; --percentage-75: .75; --percentage-80: .8; --percentage-85: .85; --percentage-90: .9; --percentage-95: .95; --percentage-100: 1; --percentage-translate-full: 1; --animate-in: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none); --animate-out: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none); @keyframes enter { from { opacity: var(--tw-enter-opacity,1); transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0)); }}@keyframes exit { to { opacity: var(--tw-exit-opacity,1); transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0)); }}--animate-accordion-down: accordion-down var(--tw-animation-duration,var(--tw-duration,.2s))ease-out; --animate-accordion-up: accordion-up var(--tw-animation-duration,var(--tw-duration,.2s))ease-out; --animate-collapsible-down: collapsible-down var(--tw-animation-duration,var(--tw-duration,.2s))ease-out; --animate-collapsible-up: collapsible-up var(--tw-animation-duration,var(--tw-duration,.2s))ease-out; @keyframes accordion-down { from { height: 0; }to { height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto)))); }}@keyframes accordion-up { from { height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto)))); }to { height: 0; }}@keyframes collapsible-down { from { height: 0; }to { height: var(--radix-collapsible-content-height,var(--bits-collapsible-content-height,var(--reka-collapsible-content-height,var(--kb-collapsible-content-height,auto)))); }}@keyframes collapsible-up { from { height: var(--radix-collapsible-content-height,var(--bits-collapsible-content-height,var(--reka-collapsible-content-height,var(--kb-collapsible-content-height,auto)))); }to { height: 0; }}--animate-caret-blink: caret-blink 1.25s ease-out infinite; @keyframes caret-blink { 0%,70%,100% { opacity: 1; }20%,50% { opacity: 0; }}}@utility animation-duration-*{--tw-animation-duration: calc(--value(number)*1ms); --tw-animation-duration: --value(--animation-duration-*,[duration],\\\"initial\\\",[*]); animation-duration: calc(--value(number)*1ms); animation-duration: --value(--animation-duration-*,[duration],\\\"initial\\\",[*]);}@utility delay-*{animation-delay: calc(--value(number)*1ms); animation-delay: --value(--animation-delay-*,[duration],\\\"initial\\\",[*]); --tw-animation-delay: calc(--value(number)*1ms); --tw-animation-delay: --value(--animation-delay-*,[duration],\\\"initial\\\",[*]);}@utility repeat-*{animation-iteration-count: --value(--animation-repeat-*,number,\\\"initial\\\",[*]); --tw-animation-iteration-count: --value(--animation-repeat-*,number,\\\"initial\\\",[*]);}@utility direction-*{animation-direction: --value(--animation-direction-*,\\\"initial\\\",[*]); --tw-animation-direction: --value(--animation-direction-*,\\\"initial\\\",[*]);}@utility fill-mode-*{animation-fill-mode: --value(--animation-fill-mode-*,\\\"initial\\\",[*]); --tw-animation-fill-mode: --value(--animation-fill-mode-*,\\\"initial\\\",[*]);}@utility running{animation-play-state: running;}@utility paused{animation-play-state: paused;}@utility play-state-*{animation-play-state: --value(\\\"initial\\\",[*]);}@utility fade-in{--tw-enter-opacity: 0;}@utility fade-in-*{--tw-enter-opacity: calc(--value(number)/100); --tw-enter-opacity: --value(--percentage-*,[*]);}@utility fade-out{--tw-exit-opacity: 0;}@utility fade-out-*{--tw-exit-opacity: calc(--value(number)/100); --tw-exit-opacity: --value(--percentage-*,[*]);}@utility zoom-in{--tw-enter-scale: 0;}@utility zoom-in-*{--tw-enter-scale: calc(--value(number)*1%); --tw-enter-scale: calc(--value(ratio)); --tw-enter-scale: --value(--percentage-*,[*]);}@utility -zoom-in-*{--tw-enter-scale: calc(--value(number)*-1%); --tw-enter-scale: calc(--value(ratio)*-1); --tw-enter-scale: --value(--percentage-*,[*]);}@utility zoom-out{--tw-exit-scale: 0;}@utility zoom-out-*{--tw-exit-scale: calc(--value(number)*1%); --tw-exit-scale: calc(--value(ratio)); --tw-exit-scale: --value(--percentage-*,[*]);}@utility -zoom-out-*{--tw-exit-scale: calc(--value(number)*-1%); --tw-exit-scale: calc(--value(ratio)*-1); --tw-exit-scale: --value(--percentage-*,[*]);}@utility spin-in{--tw-enter-rotate: 30deg;}@utility spin-in-*{--tw-enter-rotate: calc(--value(number)*1deg); --tw-enter-rotate: calc(--value(ratio)*360deg); --tw-enter-rotate: --value(--rotate-*,[*]);}@utility -spin-in{--tw-enter-rotate: -30deg;}@utility -spin-in-*{--tw-enter-rotate: calc(--value(number)*-1deg); --tw-enter-rotate: calc(--value(ratio)*-360deg); --tw-enter-rotate: --value(--rotate-*,[*]);}@utility spin-out{--tw-exit-rotate: 30deg;}@utility spin-out-*{--tw-exit-rotate: calc(--value(number)*1deg); --tw-exit-rotate: calc(--value(ratio)*360deg); --tw-exit-rotate: --value(--rotate-*,[*]);}@utility -spin-out{--tw-exit-rotate: -30deg;}@utility -spin-out-*{--tw-exit-rotate: calc(--value(number)*-1deg); --tw-exit-rotate: calc(--value(ratio)*-360deg); --tw-exit-rotate: --value(--rotate-*,[*]);}@utility slide-in-from-top{--tw-enter-translate-y: -100%;}@utility slide-in-from-top-*{--tw-enter-translate-y: calc(--value(integer)*var(--spacing)*-1); --tw-enter-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-enter-translate-y: calc(--value(ratio)*-100%); --tw-enter-translate-y: calc(--value(--translate-*,[percentage],[length])*-1);}@utility slide-in-from-bottom{--tw-enter-translate-y: 100%;}@utility slide-in-from-bottom-*{--tw-enter-translate-y: calc(--value(integer)*var(--spacing)); --tw-enter-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-enter-translate-y: calc(--value(ratio)*100%); --tw-enter-translate-y: --value(--translate-*,[percentage],[length]);}@utility slide-in-from-left{--tw-enter-translate-x: -100%;}@utility slide-in-from-left-*{--tw-enter-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-enter-translate-x: calc(--value(ratio)*-100%); --tw-enter-translate-x: calc(--value(--translate-*,[percentage],[length])*-1);}@utility slide-in-from-right{--tw-enter-translate-x: 100%;}@utility slide-in-from-right-*{--tw-enter-translate-x: calc(--value(integer)*var(--spacing)); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-enter-translate-x: calc(--value(ratio)*100%); --tw-enter-translate-x: --value(--translate-*,[percentage],[length]);}@utility slide-in-from-start{&:dir(ltr){ --tw-enter-translate-x: -100%; }&:dir(rtl){ --tw-enter-translate-x: 100%; }}@utility slide-in-from-start-*{&:where(:dir(ltr),[dir=\\\"ltr\\\"],[dir=\\\"ltr\\\"]*){ --tw-enter-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-enter-translate-x: calc(--value(ratio)*-100%); --tw-enter-translate-x: calc(--value(--translate-*,[percentage],[length])*-1); }&:where(:dir(rtl),[dir=\\\"rtl\\\"],[dir=\\\"rtl\\\"]*){ --tw-enter-translate-x: calc(--value(integer)*var(--spacing)); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-enter-translate-x: calc(--value(ratio)*100%); --tw-enter-translate-x: --value(--translate-*,[percentage],[length]); }}@utility slide-in-from-end{&:dir(ltr){ --tw-enter-translate-x: 100%; }&:dir(rtl){ --tw-enter-translate-x: -100%; }}@utility slide-in-from-end-*{&:where(:dir(ltr),[dir=\\\"ltr\\\"],[dir=\\\"ltr\\\"]*){ --tw-enter-translate-x: calc(--value(integer)*var(--spacing)); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-enter-translate-x: calc(--value(ratio)*100%); --tw-enter-translate-x: --value(--translate-*,[percentage],[length]); }&:where(:dir(rtl),[dir=\\\"rtl\\\"],[dir=\\\"rtl\\\"]*){ --tw-enter-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-enter-translate-x: calc(--value(ratio)*-100%); --tw-enter-translate-x: calc(--value(--translate-*,[percentage],[length])*-1); }}@utility slide-out-to-top{--tw-exit-translate-y: -100%;}@utility slide-out-to-top-*{--tw-exit-translate-y: calc(--value(integer)*var(--spacing)*-1); --tw-exit-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-exit-translate-y: calc(--value(ratio)*-100%); --tw-exit-translate-y: calc(--value(--translate-*,[percentage],[length])*-1);}@utility slide-out-to-bottom{--tw-exit-translate-y: 100%;}@utility slide-out-to-bottom-*{--tw-exit-translate-y: calc(--value(integer)*var(--spacing)); --tw-exit-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-exit-translate-y: calc(--value(ratio)*100%); --tw-exit-translate-y: --value(--translate-*,[percentage],[length]);}@utility slide-out-to-left{--tw-exit-translate-x: -100%;}@utility slide-out-to-left-*{--tw-exit-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-exit-translate-x: calc(--value(ratio)*-100%); --tw-exit-translate-x: calc(--value(--translate-*,[percentage],[length])*-1);}@utility slide-out-to-right{--tw-exit-translate-x: 100%;}@utility slide-out-to-right-*{--tw-exit-translate-x: calc(--value(integer)*var(--spacing)); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-exit-translate-x: calc(--value(ratio)*100%); --tw-exit-translate-x: --value(--translate-*,[percentage],[length]);}@utility slide-out-to-start{&:dir(ltr){ --tw-exit-translate-x: -100%; }&:dir(rtl){ --tw-exit-translate-x: 100%; }}@utility slide-out-to-start-*{&:where(:dir(ltr),[dir=\\\"ltr\\\"],[dir=\\\"ltr\\\"]*){ --tw-exit-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-exit-translate-x: calc(--value(ratio)*-100%); --tw-exit-translate-x: calc(--value(--translate-*,[percentage],[length])*-1); }&:where(:dir(rtl),[dir=\\\"rtl\\\"],[dir=\\\"rtl\\\"]*){ --tw-exit-translate-x: calc(--value(integer)*var(--spacing)); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-exit-translate-x: calc(--value(ratio)*100%); --tw-exit-translate-x: --value(--translate-*,[percentage],[length]); }}@utility slide-out-to-end{&:dir(ltr){ --tw-exit-translate-x: 100%; }&:dir(rtl){ --tw-exit-translate-x: -100%; }}@utility slide-out-to-end-*{&:where(:dir(ltr),[dir=\\\"ltr\\\"],[dir=\\\"ltr\\\"]*){ --tw-exit-translate-x: calc(--value(integer)*var(--spacing)); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-exit-translate-x: calc(--value(ratio)*100%); --tw-exit-translate-x: --value(--translate-*,[percentage],[length]); }&:where(:dir(rtl),[dir=\\\"rtl\\\"],[dir=\\\"rtl\\\"]*){ --tw-exit-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-exit-translate-x: calc(--value(ratio)*-100%); --tw-exit-translate-x: calc(--value(--translate-*,[percentage],[length])*-1); }}\",\"@import \\\"tailwindcss\\\";\\n@import \\\"tw-animate-css\\\";\\n\\n@custom-variant dark (&:is(.dark *));\\n\\n:root {\\n  --background: #ffffff;\\n  --foreground: #171717;\\n  --card: oklch(1 0 0);\\n  --card-foreground: oklch(0.145 0 0);\\n  --popover: oklch(1 0 0);\\n  --popover-foreground: oklch(0.145 0 0);\\n  /* --primary: oklch(0.205 0 0); */\\n\\n  --primary: oklch(24.37% 0.0951 286.37);\\n  --primary-foreground: oklch(0.985 0 0);\\n  /* --secondary: oklch(0.97 0 0); */\\n  --secondary: oklch(37.34% 0.1397 315.97);\\n\\n  --secondary-foreground: oklch(0.205 0 0);\\n  --muted: oklch(0.97 0 0);\\n  --muted-foreground: oklch(0.556 0 0);\\n  --accent: oklch(0.97 0 0);\\n  --accent-foreground: oklch(0.205 0 0);\\n  --destructive: oklch(0.577 0.245 27.325);\\n  --destructive-foreground: oklch(0.577 0.245 27.325);\\n  --border: oklch(0.922 0 0);\\n  --input: oklch(0.922 0 0);\\n  --ring: oklch(0.708 0 0);\\n  --chart-1: oklch(0.646 0.222 41.116);\\n  --chart-2: oklch(0.6 0.118 184.704);\\n  --chart-3: oklch(0.398 0.07 227.392);\\n  --chart-4: oklch(0.828 0.189 84.429);\\n  --chart-5: oklch(0.769 0.188 70.08);\\n  --radius: 0.625rem;\\n  --sidebar: oklch(0.985 0 0);\\n  --sidebar-foreground: oklch(0.145 0 0);\\n  --sidebar-primary: oklch(0.205 0 0);\\n  --sidebar-primary-foreground: oklch(0.985 0 0);\\n  --sidebar-accent: oklch(0.97 0 0);\\n  --sidebar-accent-foreground: oklch(0.205 0 0);\\n  --sidebar-border: oklch(0.922 0 0);\\n  --sidebar-ring: oklch(0.708 0 0);\\n\\n  --dccpink: oklch(0.54 0.216689 5.2);\\n  --dccblue: oklch(0.24 0.0951 286.37);\\n  --dcclightblue: oklch(0.64 0.1154 218.6);\\n  --dccviolet: oklch(0.45 0.1883 326.95);\\n  --dccpurple: oklch(0.37 0.1397 315.97);\\n  --dcclightgrey: oklch(0.7 0.0146 134.93);\\n  --dccdarkgrey: oklch(0.44 0.0031 228.84);\\n  --dccyellow: oklch(0.87 0.1768 90.38);\\n  --dccgreen: oklch(0.75 0.1806 124.9);\\n  --dccgrey: oklch(0.7 0.0146 134.93);\\n  --dccorange: oklch(0.75 0.1674 64.79);\\n  --dcclightorange: oklch(0.83 0.1464 73.9);\\n}\\n\\n@theme inline {\\n  --color-background: var(--background);\\n  --color-foreground: var(--foreground);\\n  --font-sans: var(--font-geist-sans);\\n  --font-mono: var(--font-geist-mono);\\n  --color-sidebar-ring: var(--sidebar-ring);\\n  --color-sidebar-border: var(--sidebar-border);\\n  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);\\n  --color-sidebar-accent: var(--sidebar-accent);\\n  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);\\n  --color-sidebar-primary: var(--sidebar-primary);\\n  --color-sidebar-foreground: var(--sidebar-foreground);\\n  --color-sidebar: var(--sidebar);\\n  --color-chart-5: var(--chart-5);\\n  --color-chart-4: var(--chart-4);\\n  --color-chart-3: var(--chart-3);\\n  --color-chart-2: var(--chart-2);\\n  --color-chart-1: var(--chart-1);\\n  --color-ring: var(--ring);\\n  --color-input: var(--input);\\n  --color-border: var(--border);\\n  --color-destructive-foreground: var(--destructive-foreground);\\n  --color-destructive: var(--destructive);\\n  --color-accent-foreground: var(--accent-foreground);\\n  --color-accent: var(--accent);\\n  --color-muted-foreground: var(--muted-foreground);\\n  --color-muted: var(--muted);\\n  --color-secondary-foreground: var(--secondary-foreground);\\n  --color-secondary: var(--secondary);\\n  --color-primary-foreground: var(--primary-foreground);\\n  --color-primary: var(--primary);\\n  --color-popover-foreground: var(--popover-foreground);\\n  --color-popover: var(--popover);\\n  --color-card-foreground: var(--card-foreground);\\n  --color-card: var(--card);\\n  --radius-sm: calc(var(--radius) - 4px);\\n  --radius-md: calc(var(--radius) - 2px);\\n  --radius-lg: var(--radius);\\n  --radius-xl: calc(var(--radius) + 4px);\\n\\n  --color-dccpink: var(--dccpink);\\n  --color-dccblue: var(--dccblue);\\n  --color-dcclightblue: var(--dcclightblue);\\n  --color-dccviolet: var(--dccviolet);\\n  --color-dccpurple: var(--dccpurple);\\n  --color-dcclightgrey: var(--dcclightgrey);\\n  --color-dccyellow: var(--dccyellow);\\n  --color-dccgreen: var(--dccgreen);\\n  --color-dccgrey: var(--dccgrey);\\n  --color-dccorange: var(--dccorange);\\n  --color-dcclightorange: var(--dcclightorange);\\n  --color-dccdarkgrey: var(--dccdarkgrey);\\n}\\n\\n@media (prefers-color-scheme: dark) {\\n  :root {\\n    /* --background: #ededed; */\\n    /* --background: #0a0a0a; */\\n    /* --foreground: #ededed; */\\n  }\\n}\\n\\nbody {\\n  background: var(--background);\\n  color: var(--foreground);\\n  font-family: Arial, Helvetica, sans-serif;\\n}\\n\\n.dark {\\n  --background: oklch(0.145 0 0);\\n  --foreground: oklch(0.985 0 0);\\n  --card: oklch(0.145 0 0);\\n  --card-foreground: oklch(0.985 0 0);\\n  --popover: oklch(0.145 0 0);\\n  --popover-foreground: oklch(0.985 0 0);\\n  --primary: oklch(0.985 0 0);\\n  --primary-foreground: oklch(0.205 0 0);\\n  --secondary: oklch(0.269 0 0);\\n  --secondary-foreground: oklch(0.985 0 0);\\n  --muted: oklch(0.269 0 0);\\n  --muted-foreground: oklch(0.708 0 0);\\n  --accent: oklch(0.269 0 0);\\n  --accent-foreground: oklch(0.985 0 0);\\n  --destructive: oklch(0.396 0.141 25.723);\\n  --destructive-foreground: oklch(0.637 0.237 25.331);\\n  --border: oklch(0.269 0 0);\\n  --input: oklch(0.269 0 0);\\n  --ring: oklch(0.439 0 0);\\n  --chart-1: oklch(0.488 0.243 264.376);\\n  --chart-2: oklch(0.696 0.17 162.48);\\n  --chart-3: oklch(0.769 0.188 70.08);\\n  --chart-4: oklch(0.627 0.265 303.9);\\n  --chart-5: oklch(0.645 0.246 16.439);\\n  --sidebar: oklch(0.205 0 0);\\n  --sidebar-foreground: oklch(0.985 0 0);\\n  --sidebar-primary: oklch(0.488 0.243 264.376);\\n  --sidebar-primary-foreground: oklch(0.985 0 0);\\n  --sidebar-accent: oklch(0.269 0 0);\\n  --sidebar-accent-foreground: oklch(0.985 0 0);\\n  --sidebar-border: oklch(0.269 0 0);\\n  --sidebar-ring: oklch(0.439 0 0);\\n}\\n\\n@layer base {\\n  * {\\n    @apply border-border outline-ring/50;\\n  }\\n  body {\\n    @apply bg-background text-foreground;\\n  }\\n}\\n\\n@layer base {\\n  :root {\\n    --sidebar-background: 0 0% 98%;\\n    --sidebar-foreground: 240 5.3% 26.1%;\\n    --sidebar-primary: 240 5.9% 10%;\\n    --sidebar-primary-foreground: 0 0% 98%;\\n    --sidebar-accent: 240 4.8% 95.9%;\\n    --sidebar-accent-foreground: 240 5.9% 10%;\\n    --sidebar-border: 220 13% 91%;\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\n  }\\n\\n  .dark {\\n    --sidebar-background: 240 5.9% 10%;\\n    --sidebar-foreground: 240 4.8% 95.9%;\\n    --sidebar-primary: 224.3 76.3% 48%;\\n    --sidebar-primary-foreground: 0 0% 100%;\\n    --sidebar-accent: 240 3.7% 15.9%;\\n    --sidebar-accent-foreground: 240 4.8% 95.9%;\\n    --sidebar-border: 240 3.7% 15.9%;\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ })

});