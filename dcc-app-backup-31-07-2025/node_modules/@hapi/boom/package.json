{"name": "@hapi/boom", "description": "HTTP-friendly error objects", "version": "9.1.4", "repository": "git://github.com/hapijs/boom", "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": ["error", "http"], "files": ["lib"], "dependencies": {"@hapi/hoek": "9.x.x"}, "devDependencies": {"@hapi/code": "8.x.x", "@hapi/lab": "24.x.x", "typescript": "~4.0.2"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}