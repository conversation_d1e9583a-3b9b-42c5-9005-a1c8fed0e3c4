{"version": 3, "sources": ["../src/accordion.tsx"], "sourcesContent": ["import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as CollapsiblePrimitive from '@radix-ui/react-collapsible';\nimport { createCollapsibleScope } from '@radix-ui/react-collapsible';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * Accordion\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACCORDION_NAME = 'Accordion';\nconst ACCORDION_KEYS = ['Home', 'End', 'ArrowDown', 'ArrowUp', 'ArrowLeft', 'ArrowRight'];\n\nconst [Collection, useCollection, createCollectionScope] =\n  createCollection<AccordionTriggerElement>(ACCORDION_NAME);\n\ntype ScopedProps<P> = P & { __scopeAccordion?: Scope };\nconst [createAccordionContext, createAccordionScope] = createContextScope(ACCORDION_NAME, [\n  createCollectionScope,\n  createCollapsibleScope,\n]);\nconst useCollapsibleScope = createCollapsibleScope();\n\ntype AccordionElement = AccordionImplMultipleElement | AccordionImplSingleElement;\ninterface AccordionSingleProps extends AccordionImplSingleProps {\n  type: 'single';\n}\ninterface AccordionMultipleProps extends AccordionImplMultipleProps {\n  type: 'multiple';\n}\n\nconst Accordion = React.forwardRef<AccordionElement, AccordionSingleProps | AccordionMultipleProps>(\n  (props: ScopedProps<AccordionSingleProps | AccordionMultipleProps>, forwardedRef) => {\n    const { type, ...accordionProps } = props;\n    const singleProps = accordionProps as AccordionImplSingleProps;\n    const multipleProps = accordionProps as AccordionImplMultipleProps;\n    return (\n      <Collection.Provider scope={props.__scopeAccordion}>\n        {type === 'multiple' ? (\n          <AccordionImplMultiple {...multipleProps} ref={forwardedRef} />\n        ) : (\n          <AccordionImplSingle {...singleProps} ref={forwardedRef} />\n        )}\n      </Collection.Provider>\n    );\n  }\n);\n\nAccordion.displayName = ACCORDION_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionValueContextValue = {\n  value: string[];\n  onItemOpen(value: string): void;\n  onItemClose(value: string): void;\n};\n\nconst [AccordionValueProvider, useAccordionValueContext] =\n  createAccordionContext<AccordionValueContextValue>(ACCORDION_NAME);\n\nconst [AccordionCollapsibleProvider, useAccordionCollapsibleContext] = createAccordionContext(\n  ACCORDION_NAME,\n  { collapsible: false }\n);\n\ntype AccordionImplSingleElement = AccordionImplElement;\ninterface AccordionImplSingleProps extends AccordionImplProps {\n  /**\n   * The controlled stateful value of the accordion item whose content is expanded.\n   */\n  value?: string;\n  /**\n   * The value of the item whose content is expanded when the accordion is initially rendered. Use\n   * `defaultValue` if you do not need to control the state of an accordion.\n   */\n  defaultValue?: string;\n  /**\n   * The callback that fires when the state of the accordion changes.\n   */\n  onValueChange?(value: string): void;\n  /**\n   * Whether an accordion item can be collapsed after it has been opened.\n   * @default false\n   */\n  collapsible?: boolean;\n}\n\nconst AccordionImplSingle = React.forwardRef<AccordionImplSingleElement, AccordionImplSingleProps>(\n  (props: ScopedProps<AccordionImplSingleProps>, forwardedRef) => {\n    const {\n      value: valueProp,\n      defaultValue,\n      onValueChange = () => {},\n      collapsible = false,\n      ...accordionSingleProps\n    } = props;\n\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      defaultProp: defaultValue ?? '',\n      onChange: onValueChange,\n      caller: ACCORDION_NAME,\n    });\n\n    return (\n      <AccordionValueProvider\n        scope={props.__scopeAccordion}\n        value={React.useMemo(() => (value ? [value] : []), [value])}\n        onItemOpen={setValue}\n        onItemClose={React.useCallback(() => collapsible && setValue(''), [collapsible, setValue])}\n      >\n        <AccordionCollapsibleProvider scope={props.__scopeAccordion} collapsible={collapsible}>\n          <AccordionImpl {...accordionSingleProps} ref={forwardedRef} />\n        </AccordionCollapsibleProvider>\n      </AccordionValueProvider>\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionImplMultipleElement = AccordionImplElement;\ninterface AccordionImplMultipleProps extends AccordionImplProps {\n  /**\n   * The controlled stateful value of the accordion items whose contents are expanded.\n   */\n  value?: string[];\n  /**\n   * The value of the items whose contents are expanded when the accordion is initially rendered. Use\n   * `defaultValue` if you do not need to control the state of an accordion.\n   */\n  defaultValue?: string[];\n  /**\n   * The callback that fires when the state of the accordion changes.\n   */\n  onValueChange?(value: string[]): void;\n}\n\nconst AccordionImplMultiple = React.forwardRef<\n  AccordionImplMultipleElement,\n  AccordionImplMultipleProps\n>((props: ScopedProps<AccordionImplMultipleProps>, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {},\n    ...accordionMultipleProps\n  } = props;\n\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue ?? [],\n    onChange: onValueChange,\n    caller: ACCORDION_NAME,\n  });\n\n  const handleItemOpen = React.useCallback(\n    (itemValue: string) => setValue((prevValue = []) => [...prevValue, itemValue]),\n    [setValue]\n  );\n\n  const handleItemClose = React.useCallback(\n    (itemValue: string) =>\n      setValue((prevValue = []) => prevValue.filter((value) => value !== itemValue)),\n    [setValue]\n  );\n\n  return (\n    <AccordionValueProvider\n      scope={props.__scopeAccordion}\n      value={value}\n      onItemOpen={handleItemOpen}\n      onItemClose={handleItemClose}\n    >\n      <AccordionCollapsibleProvider scope={props.__scopeAccordion} collapsible={true}>\n        <AccordionImpl {...accordionMultipleProps} ref={forwardedRef} />\n      </AccordionCollapsibleProvider>\n    </AccordionValueProvider>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionImplContextValue = {\n  disabled?: boolean;\n  direction: AccordionImplProps['dir'];\n  orientation: AccordionImplProps['orientation'];\n};\n\nconst [AccordionImplProvider, useAccordionContext] =\n  createAccordionContext<AccordionImplContextValue>(ACCORDION_NAME);\n\ntype AccordionImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface AccordionImplProps extends PrimitiveDivProps {\n  /**\n   * Whether or not an accordion is disabled from user interaction.\n   *\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * The layout in which the Accordion operates.\n   * @default vertical\n   */\n  orientation?: React.AriaAttributes['aria-orientation'];\n  /**\n   * The language read direction.\n   */\n  dir?: Direction;\n}\n\nconst AccordionImpl = React.forwardRef<AccordionImplElement, AccordionImplProps>(\n  (props: ScopedProps<AccordionImplProps>, forwardedRef) => {\n    const { __scopeAccordion, disabled, dir, orientation = 'vertical', ...accordionProps } = props;\n    const accordionRef = React.useRef<AccordionImplElement>(null);\n    const composedRefs = useComposedRefs(accordionRef, forwardedRef);\n    const getItems = useCollection(__scopeAccordion);\n    const direction = useDirection(dir);\n    const isDirectionLTR = direction === 'ltr';\n\n    const handleKeyDown = composeEventHandlers(props.onKeyDown, (event) => {\n      if (!ACCORDION_KEYS.includes(event.key)) return;\n      const target = event.target as HTMLElement;\n      const triggerCollection = getItems().filter((item) => !item.ref.current?.disabled);\n      const triggerIndex = triggerCollection.findIndex((item) => item.ref.current === target);\n      const triggerCount = triggerCollection.length;\n\n      if (triggerIndex === -1) return;\n\n      // Prevents page scroll while user is navigating\n      event.preventDefault();\n\n      let nextIndex = triggerIndex;\n      const homeIndex = 0;\n      const endIndex = triggerCount - 1;\n\n      const moveNext = () => {\n        nextIndex = triggerIndex + 1;\n        if (nextIndex > endIndex) {\n          nextIndex = homeIndex;\n        }\n      };\n\n      const movePrev = () => {\n        nextIndex = triggerIndex - 1;\n        if (nextIndex < homeIndex) {\n          nextIndex = endIndex;\n        }\n      };\n\n      switch (event.key) {\n        case 'Home':\n          nextIndex = homeIndex;\n          break;\n        case 'End':\n          nextIndex = endIndex;\n          break;\n        case 'ArrowRight':\n          if (orientation === 'horizontal') {\n            if (isDirectionLTR) {\n              moveNext();\n            } else {\n              movePrev();\n            }\n          }\n          break;\n        case 'ArrowDown':\n          if (orientation === 'vertical') {\n            moveNext();\n          }\n          break;\n        case 'ArrowLeft':\n          if (orientation === 'horizontal') {\n            if (isDirectionLTR) {\n              movePrev();\n            } else {\n              moveNext();\n            }\n          }\n          break;\n        case 'ArrowUp':\n          if (orientation === 'vertical') {\n            movePrev();\n          }\n          break;\n      }\n\n      const clampedIndex = nextIndex % triggerCount;\n      triggerCollection[clampedIndex]!.ref.current?.focus();\n    });\n\n    return (\n      <AccordionImplProvider\n        scope={__scopeAccordion}\n        disabled={disabled}\n        direction={dir}\n        orientation={orientation}\n      >\n        <Collection.Slot scope={__scopeAccordion}>\n          <Primitive.div\n            {...accordionProps}\n            data-orientation={orientation}\n            ref={composedRefs}\n            onKeyDown={disabled ? undefined : handleKeyDown}\n          />\n        </Collection.Slot>\n      </AccordionImplProvider>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'AccordionItem';\n\ntype AccordionItemContextValue = { open?: boolean; disabled?: boolean; triggerId: string };\nconst [AccordionItemProvider, useAccordionItemContext] =\n  createAccordionContext<AccordionItemContextValue>(ITEM_NAME);\n\ntype AccordionItemElement = React.ComponentRef<typeof CollapsiblePrimitive.Root>;\ntype CollapsibleProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Root>;\ninterface AccordionItemProps\n  extends Omit<CollapsibleProps, 'open' | 'defaultOpen' | 'onOpenChange'> {\n  /**\n   * Whether or not an accordion item is disabled from user interaction.\n   *\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * A string value for the accordion item. All items within an accordion should use a unique value.\n   */\n  value: string;\n}\n\n/**\n * `AccordionItem` contains all of the parts of a collapsible section inside of an `Accordion`.\n */\nconst AccordionItem = React.forwardRef<AccordionItemElement, AccordionItemProps>(\n  (props: ScopedProps<AccordionItemProps>, forwardedRef) => {\n    const { __scopeAccordion, value, ...accordionItemProps } = props;\n    const accordionContext = useAccordionContext(ITEM_NAME, __scopeAccordion);\n    const valueContext = useAccordionValueContext(ITEM_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    const triggerId = useId();\n    const open = (value && valueContext.value.includes(value)) || false;\n    const disabled = accordionContext.disabled || props.disabled;\n\n    return (\n      <AccordionItemProvider\n        scope={__scopeAccordion}\n        open={open}\n        disabled={disabled}\n        triggerId={triggerId}\n      >\n        <CollapsiblePrimitive.Root\n          data-orientation={accordionContext.orientation}\n          data-state={getState(open)}\n          {...collapsibleScope}\n          {...accordionItemProps}\n          ref={forwardedRef}\n          disabled={disabled}\n          open={open}\n          onOpenChange={(open) => {\n            if (open) {\n              valueContext.onItemOpen(value);\n            } else {\n              valueContext.onItemClose(value);\n            }\n          }}\n        />\n      </AccordionItemProvider>\n    );\n  }\n);\n\nAccordionItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionHeader\n * -----------------------------------------------------------------------------------------------*/\n\nconst HEADER_NAME = 'AccordionHeader';\n\ntype AccordionHeaderElement = React.ComponentRef<typeof Primitive.h3>;\ntype PrimitiveHeading3Props = React.ComponentPropsWithoutRef<typeof Primitive.h3>;\ninterface AccordionHeaderProps extends PrimitiveHeading3Props {}\n\n/**\n * `AccordionHeader` contains the content for the parts of an `AccordionItem` that will be visible\n * whether or not its content is collapsed.\n */\nconst AccordionHeader = React.forwardRef<AccordionHeaderElement, AccordionHeaderProps>(\n  (props: ScopedProps<AccordionHeaderProps>, forwardedRef) => {\n    const { __scopeAccordion, ...headerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(HEADER_NAME, __scopeAccordion);\n    return (\n      <Primitive.h3\n        data-orientation={accordionContext.orientation}\n        data-state={getState(itemContext.open)}\n        data-disabled={itemContext.disabled ? '' : undefined}\n        {...headerProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nAccordionHeader.displayName = HEADER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'AccordionTrigger';\n\ntype AccordionTriggerElement = React.ComponentRef<typeof CollapsiblePrimitive.Trigger>;\ntype CollapsibleTriggerProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Trigger>;\ninterface AccordionTriggerProps extends CollapsibleTriggerProps {}\n\n/**\n * `AccordionTrigger` is the trigger that toggles the collapsed state of an `AccordionItem`. It\n * should always be nested inside of an `AccordionHeader`.\n */\nconst AccordionTrigger = React.forwardRef<AccordionTriggerElement, AccordionTriggerProps>(\n  (props: ScopedProps<AccordionTriggerProps>, forwardedRef) => {\n    const { __scopeAccordion, ...triggerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleContext = useAccordionCollapsibleContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return (\n      <Collection.ItemSlot scope={__scopeAccordion}>\n        <CollapsiblePrimitive.Trigger\n          aria-disabled={(itemContext.open && !collapsibleContext.collapsible) || undefined}\n          data-orientation={accordionContext.orientation}\n          id={itemContext.triggerId}\n          {...collapsibleScope}\n          {...triggerProps}\n          ref={forwardedRef}\n        />\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nAccordionTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'AccordionContent';\n\ntype AccordionContentElement = React.ComponentRef<typeof CollapsiblePrimitive.Content>;\ntype CollapsibleContentProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Content>;\ninterface AccordionContentProps extends CollapsibleContentProps {}\n\n/**\n * `AccordionContent` contains the collapsible content for an `AccordionItem`.\n */\nconst AccordionContent = React.forwardRef<AccordionContentElement, AccordionContentProps>(\n  (props: ScopedProps<AccordionContentProps>, forwardedRef) => {\n    const { __scopeAccordion, ...contentProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(CONTENT_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return (\n      <CollapsiblePrimitive.Content\n        role=\"region\"\n        aria-labelledby={itemContext.triggerId}\n        data-orientation={accordionContext.orientation}\n        {...collapsibleScope}\n        {...contentProps}\n        ref={forwardedRef}\n        style={{\n          ['--radix-accordion-content-height' as any]: 'var(--radix-collapsible-content-height)',\n          ['--radix-accordion-content-width' as any]: 'var(--radix-collapsible-content-width)',\n          ...props.style,\n        }}\n      />\n    );\n  }\n);\n\nAccordionContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open?: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst Root = Accordion;\nconst Item = AccordionItem;\nconst Header = AccordionHeader;\nconst Trigger = AccordionTrigger;\nconst Content = AccordionContent;\n\nexport {\n  createAccordionScope,\n  //\n  Accordion,\n  AccordionItem,\n  AccordionHeader,\n  AccordionTrigger,\n  AccordionContent,\n  //\n  Root,\n  Item,\n  Header,\n  Trigger,\n  Content,\n};\nexport type {\n  AccordionSingleProps,\n  AccordionMultipleProps,\n  AccordionItemProps,\n  AccordionHeaderProps,\n  AccordionTriggerProps,\n  AccordionContentProps,\n};\n"], "mappings": ";;;AAAA,OAAO,WAAW;AAClB,SAAS,0BAA0B;AACnC,SAAS,wBAAwB;AACjC,SAAS,uBAAuB;AAChC,SAAS,4BAA4B;AACrC,SAAS,4BAA4B;AACrC,SAAS,iBAAiB;AAC1B,YAAY,0BAA0B;AACtC,SAAS,8BAA8B;AACvC,SAAS,aAAa;AAGtB,SAAS,oBAAoB;AAqCnB;AA7BV,IAAM,iBAAiB;AACvB,IAAM,iBAAiB,CAAC,QAAQ,OAAO,aAAa,WAAW,aAAa,YAAY;AAExF,IAAM,CAAC,YAAY,eAAe,qBAAqB,IACrD,iBAA0C,cAAc;AAG1D,IAAM,CAAC,wBAAwB,oBAAoB,IAAI,mBAAmB,gBAAgB;AAAA,EACxF;AAAA,EACA;AACF,CAAC;AACD,IAAM,sBAAsB,uBAAuB;AAUnD,IAAM,YAAY,MAAM;AAAA,EACtB,CAAC,OAAmE,iBAAiB;AACnF,UAAM,EAAE,MAAM,GAAG,eAAe,IAAI;AACpC,UAAM,cAAc;AACpB,UAAM,gBAAgB;AACtB,WACE,oBAAC,WAAW,UAAX,EAAoB,OAAO,MAAM,kBAC/B,mBAAS,aACR,oBAAC,yBAAuB,GAAG,eAAe,KAAK,cAAc,IAE7D,oBAAC,uBAAqB,GAAG,aAAa,KAAK,cAAc,GAE7D;AAAA,EAEJ;AACF;AAEA,UAAU,cAAc;AAUxB,IAAM,CAAC,wBAAwB,wBAAwB,IACrD,uBAAmD,cAAc;AAEnE,IAAM,CAAC,8BAA8B,8BAA8B,IAAI;AAAA,EACrE;AAAA,EACA,EAAE,aAAa,MAAM;AACvB;AAwBA,IAAM,sBAAsB,MAAM;AAAA,EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM;AAAA,MACJ,OAAO;AAAA,MACP;AAAA,MACA,gBAAgB,MAAM;AAAA,MAAC;AAAA,MACvB,cAAc;AAAA,MACd,GAAG;AAAA,IACL,IAAI;AAEJ,UAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;AAAA,MAC7C,MAAM;AAAA,MACN,aAAa,gBAAgB;AAAA,MAC7B,UAAU;AAAA,MACV,QAAQ;AAAA,IACV,CAAC;AAED,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,MAAM;AAAA,QACb,OAAO,MAAM,QAAQ,MAAO,QAAQ,CAAC,KAAK,IAAI,CAAC,GAAI,CAAC,KAAK,CAAC;AAAA,QAC1D,YAAY;AAAA,QACZ,aAAa,MAAM,YAAY,MAAM,eAAe,SAAS,EAAE,GAAG,CAAC,aAAa,QAAQ,CAAC;AAAA,QAEzF,8BAAC,gCAA6B,OAAO,MAAM,kBAAkB,aAC3D,8BAAC,iBAAe,GAAG,sBAAsB,KAAK,cAAc,GAC9D;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAqBA,IAAM,wBAAwB,MAAM,WAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM;AAAA,IACJ,OAAO;AAAA,IACP;AAAA,IACA,gBAAgB,MAAM;AAAA,IAAC;AAAA,IACvB,GAAG;AAAA,EACL,IAAI;AAEJ,QAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;AAAA,IAC7C,MAAM;AAAA,IACN,aAAa,gBAAgB,CAAC;AAAA,IAC9B,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AAED,QAAM,iBAAiB,MAAM;AAAA,IAC3B,CAAC,cAAsB,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,WAAW,SAAS,CAAC;AAAA,IAC7E,CAAC,QAAQ;AAAA,EACX;AAEA,QAAM,kBAAkB,MAAM;AAAA,IAC5B,CAAC,cACC,SAAS,CAAC,YAAY,CAAC,MAAM,UAAU,OAAO,CAACA,WAAUA,WAAU,SAAS,CAAC;AAAA,IAC/E,CAAC,QAAQ;AAAA,EACX;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,MAAM;AAAA,MACb;AAAA,MACA,YAAY;AAAA,MACZ,aAAa;AAAA,MAEb,8BAAC,gCAA6B,OAAO,MAAM,kBAAkB,aAAa,MACxE,8BAAC,iBAAe,GAAG,wBAAwB,KAAK,cAAc,GAChE;AAAA;AAAA,EACF;AAEJ,CAAC;AAUD,IAAM,CAAC,uBAAuB,mBAAmB,IAC/C,uBAAkD,cAAc;AAsBlE,IAAM,gBAAgB,MAAM;AAAA,EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,kBAAkB,UAAU,KAAK,cAAc,YAAY,GAAG,eAAe,IAAI;AACzF,UAAM,eAAe,MAAM,OAA6B,IAAI;AAC5D,UAAM,eAAe,gBAAgB,cAAc,YAAY;AAC/D,UAAM,WAAW,cAAc,gBAAgB;AAC/C,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,iBAAiB,cAAc;AAErC,UAAM,gBAAgB,qBAAqB,MAAM,WAAW,CAAC,UAAU;AACrE,UAAI,CAAC,eAAe,SAAS,MAAM,GAAG,EAAG;AACzC,YAAM,SAAS,MAAM;AACrB,YAAM,oBAAoB,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,IAAI,SAAS,QAAQ;AACjF,YAAM,eAAe,kBAAkB,UAAU,CAAC,SAAS,KAAK,IAAI,YAAY,MAAM;AACtF,YAAM,eAAe,kBAAkB;AAEvC,UAAI,iBAAiB,GAAI;AAGzB,YAAM,eAAe;AAErB,UAAI,YAAY;AAChB,YAAM,YAAY;AAClB,YAAM,WAAW,eAAe;AAEhC,YAAM,WAAW,MAAM;AACrB,oBAAY,eAAe;AAC3B,YAAI,YAAY,UAAU;AACxB,sBAAY;AAAA,QACd;AAAA,MACF;AAEA,YAAM,WAAW,MAAM;AACrB,oBAAY,eAAe;AAC3B,YAAI,YAAY,WAAW;AACzB,sBAAY;AAAA,QACd;AAAA,MACF;AAEA,cAAQ,MAAM,KAAK;AAAA,QACjB,KAAK;AACH,sBAAY;AACZ;AAAA,QACF,KAAK;AACH,sBAAY;AACZ;AAAA,QACF,KAAK;AACH,cAAI,gBAAgB,cAAc;AAChC,gBAAI,gBAAgB;AAClB,uBAAS;AAAA,YACX,OAAO;AACL,uBAAS;AAAA,YACX;AAAA,UACF;AACA;AAAA,QACF,KAAK;AACH,cAAI,gBAAgB,YAAY;AAC9B,qBAAS;AAAA,UACX;AACA;AAAA,QACF,KAAK;AACH,cAAI,gBAAgB,cAAc;AAChC,gBAAI,gBAAgB;AAClB,uBAAS;AAAA,YACX,OAAO;AACL,uBAAS;AAAA,YACX;AAAA,UACF;AACA;AAAA,QACF,KAAK;AACH,cAAI,gBAAgB,YAAY;AAC9B,qBAAS;AAAA,UACX;AACA;AAAA,MACJ;AAEA,YAAM,eAAe,YAAY;AACjC,wBAAkB,YAAY,EAAG,IAAI,SAAS,MAAM;AAAA,IACtD,CAAC;AAED,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP;AAAA,QACA,WAAW;AAAA,QACX;AAAA,QAEA,8BAAC,WAAW,MAAX,EAAgB,OAAO,kBACtB;AAAA,UAAC,UAAU;AAAA,UAAV;AAAA,YACE,GAAG;AAAA,YACJ,oBAAkB;AAAA,YAClB,KAAK;AAAA,YACL,WAAW,WAAW,SAAY;AAAA;AAAA,QACpC,GACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAMA,IAAM,YAAY;AAGlB,IAAM,CAAC,uBAAuB,uBAAuB,IACnD,uBAAkD,SAAS;AAqB7D,IAAM,gBAAgB,MAAM;AAAA,EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,kBAAkB,OAAO,GAAG,mBAAmB,IAAI;AAC3D,UAAM,mBAAmB,oBAAoB,WAAW,gBAAgB;AACxE,UAAM,eAAe,yBAAyB,WAAW,gBAAgB;AACzE,UAAM,mBAAmB,oBAAoB,gBAAgB;AAC7D,UAAM,YAAY,MAAM;AACxB,UAAM,OAAQ,SAAS,aAAa,MAAM,SAAS,KAAK,KAAM;AAC9D,UAAM,WAAW,iBAAiB,YAAY,MAAM;AAEpD,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,UAAsB;AAAA,UAArB;AAAA,YACC,oBAAkB,iBAAiB;AAAA,YACnC,cAAY,SAAS,IAAI;AAAA,YACxB,GAAG;AAAA,YACH,GAAG;AAAA,YACJ,KAAK;AAAA,YACL;AAAA,YACA;AAAA,YACA,cAAc,CAACC,UAAS;AACtB,kBAAIA,OAAM;AACR,6BAAa,WAAW,KAAK;AAAA,cAC/B,OAAO;AACL,6BAAa,YAAY,KAAK;AAAA,cAChC;AAAA,YACF;AAAA;AAAA,QACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,cAAc;AAUpB,IAAM,kBAAkB,MAAM;AAAA,EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,EAAE,kBAAkB,GAAG,YAAY,IAAI;AAC7C,UAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;AAC7E,UAAM,cAAc,wBAAwB,aAAa,gBAAgB;AACzE,WACE;AAAA,MAAC,UAAU;AAAA,MAAV;AAAA,QACC,oBAAkB,iBAAiB;AAAA,QACnC,cAAY,SAAS,YAAY,IAAI;AAAA,QACrC,iBAAe,YAAY,WAAW,KAAK;AAAA,QAC1C,GAAG;AAAA,QACJ,KAAK;AAAA;AAAA,IACP;AAAA,EAEJ;AACF;AAEA,gBAAgB,cAAc;AAM9B,IAAM,eAAe;AAUrB,IAAM,mBAAmB,MAAM;AAAA,EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,kBAAkB,GAAG,aAAa,IAAI;AAC9C,UAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;AAC7E,UAAM,cAAc,wBAAwB,cAAc,gBAAgB;AAC1E,UAAM,qBAAqB,+BAA+B,cAAc,gBAAgB;AACxF,UAAM,mBAAmB,oBAAoB,gBAAgB;AAC7D,WACE,oBAAC,WAAW,UAAX,EAAoB,OAAO,kBAC1B;AAAA,MAAsB;AAAA,MAArB;AAAA,QACC,iBAAgB,YAAY,QAAQ,CAAC,mBAAmB,eAAgB;AAAA,QACxE,oBAAkB,iBAAiB;AAAA,QACnC,IAAI,YAAY;AAAA,QACf,GAAG;AAAA,QACH,GAAG;AAAA,QACJ,KAAK;AAAA;AAAA,IACP,GACF;AAAA,EAEJ;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,eAAe;AASrB,IAAM,mBAAmB,MAAM;AAAA,EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,kBAAkB,GAAG,aAAa,IAAI;AAC9C,UAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;AAC7E,UAAM,cAAc,wBAAwB,cAAc,gBAAgB;AAC1E,UAAM,mBAAmB,oBAAoB,gBAAgB;AAC7D,WACE;AAAA,MAAsB;AAAA,MAArB;AAAA,QACC,MAAK;AAAA,QACL,mBAAiB,YAAY;AAAA,QAC7B,oBAAkB,iBAAiB;AAAA,QAClC,GAAG;AAAA,QACH,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,OAAO;AAAA,UACL,CAAC,kCAAyC,GAAG;AAAA,UAC7C,CAAC,iCAAwC,GAAG;AAAA,UAC5C,GAAG,MAAM;AAAA,QACX;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,iBAAiB,cAAc;AAI/B,SAAS,SAAS,MAAgB;AAChC,SAAO,OAAO,SAAS;AACzB;AAEA,IAAMC,QAAO;AACb,IAAM,OAAO;AACb,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,WAAU;", "names": ["value", "open", "Root", "<PERSON><PERSON>", "Content"]}